"""
Quick test to verify AI integration fix
"""

import asyncio
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_ai_integration():
    """Test AI integration with fixed methods"""
    try:
        print("🤖 Testing AI integration fix...")
        
        from ai.ollama_hub import OllamaHub
        
        # Initialize Ollama Hub
        ollama_config = {
            'models': ['exaone-deep:32b', 'phi4-reasoning:plus'],
            'load_balancing': True,
            'fallback_enabled': True
        }
        
        ollama_hub = OllamaHub(ollama_config)
        init_result = await ollama_hub.initialize()
        
        if init_result:
            print("✅ Ollama Hub initialized successfully")
            
            # Test AI response generation
            prompt = "Analyze AAPL stock: Current price $150, RSI 65. Should we buy, sell, or hold?"
            
            print(f"🎯 Testing AI response generation...")
            print(f"   Prompt: {prompt}")
            
            response = await ollama_hub.generate_response(
                prompt=prompt,
                model='exaone-deep:32b'
            )
            
            if response and len(response) > 20:
                print(f"✅ AI Response Generated Successfully!")
                print(f"   Response length: {len(response)} characters")
                print(f"   Preview: {response[:100]}...")
                
                # Test stop method
                await ollama_hub.stop()
                print("✅ Ollama Hub stopped successfully")
                
                return True
            else:
                print("❌ AI response generation failed")
                return False
                
        else:
            print("❌ Ollama Hub initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ AI integration test failed: {e}")
        return False


async def main():
    """Main test function"""
    print("\n🎯 AI INTEGRATION FIX TEST")
    print("=" * 50)
    
    success = await test_ai_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 AI INTEGRATION FIX: SUCCESS!")
        print("✅ Real AI decision making is now operational!")
    else:
        print("❌ AI INTEGRATION FIX: FAILED!")
        print("🔧 Additional fixes may be needed")
    print("=" * 50)
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
