#!/usr/bin/env python3
"""
AI Team Coordination Test
Demonstrates ONE coordinated AI team working together with shared portfolio
"""

import asyncio
import logging
import time
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import components
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from simulation.market_simulator import MarketSimulator
from simulation.ai_trading_integration import AITradingIntegration
from simulation.performance_analytics import PerformanceAnalytics
from simulation.ai_team_coordinator import AITeamCoordinator, AgentRole


async def run_ai_team_coordination_test():
    """Test the coordinated AI team trading system"""
    print("🤖 TESTING AI TEAM COORDINATION")
    print("=" * 80)
    print("🎯 ONE AI TEAM - SHARED PORTFOLIO - COORDINATED DECISIONS")
    print("=" * 80)
    
    # Configuration
    config = {
        'simulation': {
            'mode': 'real_time',
            'duration_days': 1,
            'initial_cash': 500000,  # $500k for the team
            'symbols': ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'NVDA', 'META', 'SPY'],
            'time_acceleration': 5.0,  # 5x speed
            'commission_rate': 0.001,
            'slippage_factor': 0.0005
        },
        'market_data': {
            'update_frequency': 2.0,
            'news_frequency': 120
        },
        'trading': {
            'commission_rate': 0.001,
            'slippage_factor': 0.0005
        },
        'risk': {
            'monitoring_interval': 10
        },
        'analytics': {
            'snapshot_interval': 30,
            'benchmark': 'SPY'
        }
    }
    
    try:
        # Initialize simulation components
        print("📊 Initializing Market Simulator...")
        market_simulator = MarketSimulator(config)
        await market_simulator.initialize()
        
        print("🤖 Initializing AI Trading Integration...")
        ai_integration = AITradingIntegration(config, market_simulator)
        await ai_integration.initialize()
        
        print("📈 Initializing Performance Analytics...")
        analytics = PerformanceAnalytics(config, market_simulator, ai_integration)
        await analytics.initialize()
        
        print("👥 Initializing AI Team Coordinator...")
        team_coordinator = AITeamCoordinator(config, market_simulator, ai_integration)
        await team_coordinator.initialize()
        
        # Start all systems
        print("🚀 Starting simulation systems...")
        simulation_id = await market_simulator.start_simulation()
        await ai_integration.start()
        await analytics.start()
        await team_coordinator.start_team_operations()
        
        print(f"✅ Simulation {simulation_id} running with coordinated AI team")
        print("\n🤖 AI TEAM COMPOSITION:")
        print("=" * 50)
        
        team_status = await team_coordinator.get_team_status()
        for role, agent_info in team_status['agents'].items():
            print(f"👤 {role.upper()}")
            print(f"   🧠 Model: {agent_info['model']}")
            print(f"   🎯 Agent ID: {agent_info['agent_id']}")
            print(f"   📋 Responsibilities: {', '.join(agent_info['responsibilities'])}")
            print()
            
        print("💰 SHARED PORTFOLIO:")
        portfolio = team_status['shared_portfolio']
        if portfolio:
            print(f"   💵 Total Value: ${portfolio['total_value']:,.2f}")
            print(f"   💸 Cash Balance: ${portfolio['cash_balance']:,.2f}")
            print(f"   📊 P&L: ${portfolio['total_pnl']:,.2f}")
            print(f"   📍 Positions: {portfolio['positions_count']}")
        else:
            print("   📊 Portfolio data loading...")
            
        print("\n⏱️  Running team coordination for 90 seconds...")
        print("🔄 Watching team decision-making process...")
        
        # Run simulation for 90 seconds
        start_time = time.time()
        status_interval = 20  # Print status every 20 seconds
        last_status = start_time
        
        while time.time() - start_time < 90:  # 90 seconds
            current_time = time.time()
            
            # Print status updates
            if current_time - last_status >= status_interval:
                elapsed = int(current_time - start_time)
                print(f"\n{'='*60}")
                print(f"⏰ Team Status Update - {elapsed}s elapsed")
                print(f"{'='*60}")
                
                # Get simulation status
                sim_status = await market_simulator.get_simulation_status()
                print(f"📊 Market Regime: {sim_status['market_regime']}")
                print(f"📈 Volatility Index: {sim_status['volatility_index']:.1f}")
                print(f"💹 Total Trades: {sim_status['total_trades']}")
                
                # Get team status
                team_status = await team_coordinator.get_team_status()
                portfolio = team_status['shared_portfolio']
                
                if portfolio:
                    print(f"\n🤖 AI TEAM PERFORMANCE:")
                    print(f"   💰 Portfolio Value: ${portfolio['total_value']:,.2f}")
                    print(f"   📊 P&L: ${portfolio['total_pnl']:,.2f} ({portfolio['total_return']:.2%})")
                    print(f"   📍 Active Positions: {portfolio['positions_count']}")
                    
                    if portfolio.get('positions'):
                        print(f"   📋 Current Positions:")
                        for symbol, pos in portfolio['positions'].items():
                            pnl_pct = pos.get('unrealized_pnl_pct', 0) * 100
                            print(f"      {symbol}: {pos['quantity']:.1f} shares @ ${pos['avg_price']:.2f} (P&L: {pnl_pct:+.1f}%)")
                            
                # Show recent team decisions
                recent_decisions = team_status.get('recent_decisions', [])
                if recent_decisions:
                    print(f"\n🎯 RECENT TEAM DECISIONS:")
                    for decision in recent_decisions[-3:]:  # Last 3 decisions
                        decision_time = datetime.fromtimestamp(decision.timestamp).strftime("%H:%M:%S")
                        print(f"   {decision_time}: {decision.action.upper()} {decision.quantity:.1f} {decision.symbol}")
                        print(f"      💡 Reasoning: {decision.reasoning}")
                        print(f"      🎯 Confidence: {decision.confidence:.1%}")
                        print(f"      👥 Contributing Agents: {len(decision.contributing_agents)}")
                        
                # Show team consensus
                consensus = team_status.get('team_consensus', {})
                if consensus:
                    print(f"\n🤝 TEAM CONSENSUS:")
                    for metric, value in consensus.items():
                        print(f"   {metric}: {value:.1%}")
                        
                last_status = current_time
                
            await asyncio.sleep(1)
            
        print(f"\n{'='*80}")
        print("🏁 AI TEAM COORDINATION TEST COMPLETED")
        print(f"{'='*80}")
        
        # Final team report
        final_team_status = await team_coordinator.get_team_status()
        final_portfolio = final_team_status['shared_portfolio']
        
        print(f"\n📊 FINAL TEAM PERFORMANCE REPORT")
        print("-" * 50)
        
        if final_portfolio:
            initial_value = 500000  # Starting value
            final_value = final_portfolio['total_value']
            total_return = (final_value - initial_value) / initial_value
            
            print(f"💰 Final Portfolio Value: ${final_value:,.2f}")
            print(f"💵 Cash Balance: ${final_portfolio['cash_balance']:,.2f}")
            print(f"📊 Total P&L: ${final_portfolio['total_pnl']:,.2f}")
            print(f"📈 Total Return: {total_return:.2%}")
            print(f"📍 Final Positions: {final_portfolio['positions_count']}")
            
            if final_portfolio.get('positions'):
                print(f"\n📋 Final Position Details:")
                for symbol, pos in final_portfolio['positions'].items():
                    market_value = pos['market_value']
                    unrealized_pnl = pos['unrealized_pnl']
                    pnl_pct = (unrealized_pnl / (pos['quantity'] * pos['avg_price'])) * 100 if pos['quantity'] * pos['avg_price'] > 0 else 0
                    
                    print(f"   {symbol}:")
                    print(f"      Quantity: {pos['quantity']:.2f} shares")
                    print(f"      Avg Price: ${pos['avg_price']:.2f}")
                    print(f"      Market Value: ${market_value:,.2f}")
                    print(f"      Unrealized P&L: ${unrealized_pnl:,.2f} ({pnl_pct:+.1f}%)")
                    
        # Team decision summary
        all_decisions = final_team_status.get('recent_decisions', [])
        if all_decisions:
            print(f"\n🎯 TEAM DECISION SUMMARY:")
            print(f"   Total Decisions Made: {len(all_decisions)}")
            
            # Analyze decision types
            buy_decisions = len([d for d in all_decisions if d.action == 'buy'])
            sell_decisions = len([d for d in all_decisions if d.action == 'sell'])
            
            print(f"   Buy Decisions: {buy_decisions}")
            print(f"   Sell Decisions: {sell_decisions}")
            
            if all_decisions:
                avg_confidence = sum(d.confidence for d in all_decisions) / len(all_decisions)
                print(f"   Average Confidence: {avg_confidence:.1%}")
                
        # Agent contribution analysis
        print(f"\n👥 AGENT CONTRIBUTION ANALYSIS:")
        agent_info = final_team_status['agents']
        for role, info in agent_info.items():
            print(f"   {role.upper()}: {info['model']}")
            print(f"      Status: ✅ Active and Contributing")
            
        print(f"\n🎉 TEAM COORDINATION SUCCESSFUL!")
        print(f"✅ All agents worked together as one coordinated team")
        print(f"✅ Shared portfolio managed collectively")
        print(f"✅ Decisions made through team consensus")
        print(f"✅ Risk management integrated into decision process")
        
    except Exception as e:
        logger.error(f"Team coordination test failed: {e}")
        raise
        
    finally:
        # Cleanup
        print(f"\n🧹 Cleaning up team coordination test...")
        try:
            await analytics.stop()
            await ai_integration.stop()
            await market_simulator.stop_simulation()
            print("✅ Team coordination test cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")
            
    print(f"\n🚀 AI TEAM COORDINATION SYSTEM READY!")
    print(f"💪 Team Leader coordinates strategy")
    print(f"📊 Market Analyst provides insights") 
    print(f"🎯 Strategy Developer creates opportunities")
    print(f"🛡️ Risk Manager protects capital")
    print(f"⚡ Execution Specialist optimizes trades")


if __name__ == "__main__":
    asyncio.run(run_ai_team_coordination_test())
