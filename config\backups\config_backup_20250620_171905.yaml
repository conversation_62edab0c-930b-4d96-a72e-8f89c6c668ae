# Advanced Ollama Trading Agent System Configuration

# System Information
system:
  name: "Advanced Ollama Trading Agent System"
  version: "1.0.0"
  environment: "development"  # development, staging, production
  debug: true
  log_level: "INFO"

# Ollama Configuration
ollama:
  base_url: "http://localhost:11434"
  timeout: 300
  max_retries: 3
  retry_delay: 1.0
  model_cache_size: 5  # Number of models to keep in memory
  
# Database Configuration
database:
  postgres:
    host: "127.0.0.1"
    port: 5432
    database: "postgres"
    username: "postgres"
    password: "Zaman2009"
    pool_size: 20
    max_overflow: 30
    ssl_mode: "disable"
    connection_string: "postgresql://postgres:Zaman2009@127.0.0.1:5432/postgres?sslmode=disable"

  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    max_connections: 100
    enabled: true

  clickhouse:
    host: "localhost"
    port: 8123
    database: "trading_system"
    username: "default"
    password: ""
    enabled: true

# API Configuration
api:
  host: "0.0.0.0"
  port: 8001
  workers: 4
  cors_enabled: true

# Agent Configuration
agents:
  max_agents: 50
  heartbeat_interval: 30  # seconds
  response_timeout: 60    # seconds
  memory_limit: "2GB"
  
  # Default agent settings
  defaults:
    model: "exaone-deep:32b"
    temperature: 0.7
    max_tokens: 2048
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0

# Team Configuration
teams:
  max_teams: 10
  team_formation_interval: 300  # seconds (5 minutes)
  team_evaluation_interval: 900  # seconds (15 minutes)
  min_team_size: 3
  max_team_size: 8
  
  # Team types and their configurations
  team_types:
    aggressive_growth:
      risk_tolerance: 0.8
      return_target: 0.15
      max_drawdown: 0.20
      
    defensive:
      risk_tolerance: 0.3
      return_target: 0.08
      max_drawdown: 0.10
      
    range_trading:
      risk_tolerance: 0.5
      return_target: 0.12
      max_drawdown: 0.15
      
    volatility_exploitation:
      risk_tolerance: 0.9
      return_target: 0.20
      max_drawdown: 0.25
      
    carry_strategy:
      risk_tolerance: 0.4
      return_target: 0.10
      max_drawdown: 0.12

# Market Data Configuration
market_data:
  providers:
    mock:
      enabled: true
      symbols:
        - "AAPL"
        - "GOOGL"
        - "MSFT"
        - "TSLA"
        - "NVDA"
      update_interval: 60

  update_interval: 60
  symbols:
    - "AAPL"
    - "GOOGL"
    - "MSFT"
    - "TSLA"
    - "NVDA"

# Portfolio Configuration
portfolio:
  initial_capital: 100000.0
  currency: "USD"

# Risk Management
risk:
  max_portfolio_risk: 0.02  # 2% of portfolio per trade
  max_position_size: 0.05   # 5% maximum position size
  max_drawdown: 0.15        # 15% maximum drawdown
    
# Performance Monitoring
monitoring:
  metrics_collection_interval: 60  # seconds
  performance_evaluation_interval: 3600  # seconds (1 hour)
  
  dashboards:
    enabled: true
    port: 8080
    refresh_interval: 30  # seconds
    
  alerts:
    enabled: true
    channels:
      - "console"
      - "file"
      # - "email"
      # - "slack"
      
  thresholds:
    high_latency: 5.0      # seconds
    low_performance: -0.05  # -5% performance threshold
    high_drawdown: 0.10     # 10% drawdown alert

# Learning and Adaptation
learning:
  enabled: true
  learning_rate: 0.001
  batch_size: 32
  evaluation_frequency: 1000  # iterations
  
  strategies:
    reinforcement_learning: true
    supervised_learning: true
    transfer_learning: true
    meta_learning: true
    
  repository:
    max_strategies: 1000
    cleanup_interval: 86400  # seconds (daily)
    backup_interval: 604800  # seconds (weekly)

# Security Configuration
security:
  encryption:
    enabled: true
    algorithm: "AES-256-GCM"
    key_rotation_interval: 2592000  # seconds (monthly)
    
  authentication:
    enabled: true
    token_expiry: 3600  # seconds (1 hour)
    max_login_attempts: 5
    
  audit:
    enabled: true
    log_all_decisions: true
    retention_period: 2592000  # seconds (30 days)

# Communication
communication:
  protocols:
    - "redis_pubsub"
    - "websocket"
    
  message_queue:
    max_queue_size: 10000
    message_ttl: 300  # seconds
    
  coordination:
    consensus_timeout: 30  # seconds
    voting_period: 60      # seconds
    quorum_percentage: 0.6 # 60% for decisions

# Execution
execution:
  enabled: true
  paper_trading: true  # Set to false for live trading
  
  brokers:
    - name: "paper_broker"
      enabled: true
      type: "simulation"
      
  order_management:
    max_orders_per_second: 10
    order_timeout: 300  # seconds
    
# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  handlers:
    console:
      enabled: true
      level: "INFO"
      
    file:
      enabled: true
      level: "DEBUG"
      filename: "logs/trading_system.log"
      max_size: "100MB"
      backup_count: 5
      
    structured:
      enabled: true
      format: "json"
      filename: "logs/structured.log"
