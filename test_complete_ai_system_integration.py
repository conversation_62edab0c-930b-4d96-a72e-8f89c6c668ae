#!/usr/bin/env python3
"""
Complete AI System Integration Test
Tests the entire system with full AI integration active
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any

# Import system components
from config.config_manager import ConfigManager
from system.system_coordinator import SystemCoordinator
from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from agents.base_agent import Agent<PERSON>ole
from teams.team_manager import TeamManager, TeamType
from strategies.strategy_manager import StrategyManager
from communication.message_broker import MessageBroker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CompleteAISystemIntegrationTest:
    """Comprehensive test suite for the complete AI trading system"""
    
    def __init__(self):
        self.system_coordinator = None
        self.test_results = {}
        self.start_time = None
        
    async def run_complete_test_suite(self) -> Dict[str, Any]:
        """Run the complete test suite"""
        try:
            self.start_time = time.time()
            logger.info("🚀 Starting Complete AI System Integration Test")
            
            # Test phases
            test_phases = [
                ("System Initialization", self._test_system_initialization),
                ("AI Integration Activation", self._test_ai_integration_activation),
                ("Component Integration", self._test_component_integration),
                ("AI Agent Functionality", self._test_ai_agent_functionality),
                ("Team Coordination", self._test_team_coordination),
                ("Strategy AI Integration", self._test_strategy_ai_integration),
                ("System Health Monitoring", self._test_system_health_monitoring),
                ("Performance Validation", self._test_performance_validation),
                ("Recovery Testing", self._test_recovery_mechanisms),
                ("Complete System Validation", self._test_complete_system_validation)
            ]
            
            # Execute test phases
            for phase_name, phase_function in test_phases:
                try:
                    logger.info(f"\n{'='*60}")
                    logger.info(f"🧪 Testing Phase: {phase_name}")
                    logger.info(f"{'='*60}")
                    
                    phase_start = time.time()
                    result = await phase_function()
                    phase_duration = time.time() - phase_start
                    
                    self.test_results[phase_name] = {
                        'success': result,
                        'duration': phase_duration,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    if result:
                        logger.info(f"✅ {phase_name} PASSED ({phase_duration:.2f}s)")
                    else:
                        logger.error(f"❌ {phase_name} FAILED ({phase_duration:.2f}s)")
                        
                except Exception as e:
                    logger.error(f"❌ {phase_name} ERROR: {e}")
                    self.test_results[phase_name] = {
                        'success': False,
                        'error': str(e),
                        'duration': 0,
                        'timestamp': datetime.now().isoformat()
                    }
            
            # Generate final report
            return await self._generate_final_report()
            
        except Exception as e:
            logger.error(f"Complete test suite failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
        finally:
            # Cleanup
            if self.system_coordinator:
                try:
                    await self.system_coordinator.stop()
                except:
                    pass
    
    async def _test_system_initialization(self) -> bool:
        """Test complete system initialization"""
        try:
            logger.info("Initializing system coordinator...")
            
            # Create and initialize system coordinator
            self.system_coordinator = SystemCoordinator('config/system_config.yaml')
            
            # Initialize the system
            init_success = await self.system_coordinator.initialize()
            if not init_success:
                logger.error("System initialization failed")
                return False
                
            logger.info("✅ System coordinator initialized")
            
            # Start the system
            start_success = await self.system_coordinator.start()
            if not start_success:
                logger.error("System start failed")
                return False
                
            logger.info("✅ System started successfully")
            
            # Verify system state
            status = await self.system_coordinator.get_system_status()
            if status.state.value != 'running':
                logger.error(f"System not in running state: {status.state.value}")
                return False
                
            logger.info("✅ System is in running state")
            return True
            
        except Exception as e:
            logger.error(f"System initialization test failed: {e}")
            return False
    
    async def _test_ai_integration_activation(self) -> bool:
        """Test AI integration activation"""
        try:
            logger.info("Activating AI integration...")
            
            # Activate AI integration
            ai_result = await self.system_coordinator.activate_ai_integration()
            
            if not ai_result.get('success', False):
                logger.error("AI integration activation failed")
                logger.error(f"AI result: {ai_result}")
                return False
                
            logger.info("✅ AI integration activated")
            
            # Verify AI integration status
            ai_status = await self.system_coordinator.get_ai_integration_status()
            
            if not ai_status.get('ai_integration_active', False):
                logger.error("AI integration not active")
                return False
                
            logger.info(f"✅ AI integration verified: {ai_status}")
            return True
            
        except Exception as e:
            logger.error(f"AI integration activation test failed: {e}")
            return False
    
    async def _test_component_integration(self) -> bool:
        """Test integration between all components"""
        try:
            logger.info("Testing component integration...")
            
            # Test each component
            components_to_test = [
                'config_manager',
                'ollama_hub', 
                'message_broker',
                'agent_manager',
                'team_manager',
                'strategy_manager',
                'data_manager',
                'risk_manager',
                'portfolio_manager',
                'execution_engine',
                'analytics_engine'
            ]
            
            component_results = {}
            
            for component_name in components_to_test:
                try:
                    component = await self.system_coordinator.get_component(component_name)
                    
                    if component:
                        # Test component health
                        if hasattr(component, 'get_stats'):
                            stats = await component.get_stats()
                            component_results[component_name] = stats.get('running', True)
                        else:
                            component_results[component_name] = True
                            
                        logger.info(f"✅ {component_name} integration verified")
                    else:
                        logger.warning(f"⚠️ {component_name} not found")
                        component_results[component_name] = False
                        
                except Exception as e:
                    logger.error(f"❌ {component_name} integration test failed: {e}")
                    component_results[component_name] = False
            
            # Calculate success rate
            successful_components = sum(1 for result in component_results.values() if result)
            total_components = len(component_results)
            success_rate = successful_components / total_components
            
            logger.info(f"Component integration success rate: {success_rate:.1%} ({successful_components}/{total_components})")
            
            return success_rate >= 0.8  # 80% success threshold
            
        except Exception as e:
            logger.error(f"Component integration test failed: {e}")
            return False
    
    async def _test_ai_agent_functionality(self) -> bool:
        """Test AI agent functionality"""
        try:
            logger.info("Testing AI agent functionality...")
            
            # Test agent interaction
            interaction_result = await self.system_coordinator.test_ai_agent_interaction()
            
            if not interaction_result.get('success', False):
                logger.error("AI agent interaction test failed")
                return False
                
            logger.info("✅ AI agent interaction test passed")
            
            # Test agent communication
            agent_manager = await self.system_coordinator.get_component('agent_manager')
            if agent_manager:
                active_agents = await agent_manager.get_active_agents()
                logger.info(f"✅ Found {len(active_agents)} active agents")
                
                if len(active_agents) == 0:
                    logger.error("No active agents found")
                    return False
            else:
                logger.error("Agent manager not available")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"AI agent functionality test failed: {e}")
            return False
    
    async def _test_team_coordination(self) -> bool:
        """Test team coordination functionality"""
        try:
            logger.info("Testing team coordination...")
            
            # Get team manager
            team_manager = await self.system_coordinator.get_component('team_manager')
            if not team_manager:
                logger.error("Team manager not available")
                return False
                
            # Test team communication stats
            comm_stats = await team_manager.get_team_communication_stats()
            
            if comm_stats.get('total_teams', 0) == 0:
                logger.error("No teams found")
                return False
                
            logger.info(f"✅ Found {comm_stats['total_teams']} teams")
            
            # Test active teams
            active_teams = await team_manager.get_active_teams()
            logger.info(f"✅ Found {len(active_teams)} active teams")
            
            return len(active_teams) > 0
            
        except Exception as e:
            logger.error(f"Team coordination test failed: {e}")
            return False

    async def _test_strategy_ai_integration(self) -> bool:
        """Test strategy AI integration"""
        try:
            logger.info("Testing strategy AI integration...")

            # Get strategy manager
            strategy_manager = await self.system_coordinator.get_component('strategy_manager')
            if not strategy_manager:
                logger.error("Strategy manager not available")
                return False

            # Test AI strategies summary
            ai_summary = await strategy_manager.get_ai_strategies_summary()

            if ai_summary.get('total_ai_strategies', 0) == 0:
                logger.error("No AI strategies found")
                return False

            logger.info(f"✅ Found {ai_summary['total_ai_strategies']} AI strategies")

            # Test AI optimization
            optimization_result = await strategy_manager.optimize_ai_strategies()

            if optimization_result.get('success', False):
                logger.info("✅ AI strategy optimization test passed")
            else:
                logger.warning("⚠️ AI strategy optimization test failed")

            return True

        except Exception as e:
            logger.error(f"Strategy AI integration test failed: {e}")
            return False

    async def _test_system_health_monitoring(self) -> bool:
        """Test system health monitoring"""
        try:
            logger.info("Testing system health monitoring...")

            # Get comprehensive health report
            health_report = await self.system_coordinator.get_comprehensive_health_report()

            overall_health = health_report.get('overall_health', 0.0)
            health_status = health_report.get('health_status', 'unknown')

            logger.info(f"System health: {overall_health:.1%} ({health_status})")

            if overall_health < 0.5:
                logger.error("System health is critical")
                return False

            # Test health monitoring components
            component_health = health_report.get('component_health', {})
            healthy_components = sum(1 for comp in component_health.values() if comp.get('running', False))
            total_components = len(component_health)

            logger.info(f"Component health: {healthy_components}/{total_components} healthy")

            return healthy_components / total_components >= 0.7 if total_components > 0 else False

        except Exception as e:
            logger.error(f"System health monitoring test failed: {e}")
            return False

    async def _test_performance_validation(self) -> bool:
        """Test performance validation"""
        try:
            logger.info("Testing performance validation...")

            # Run comprehensive AI validation
            validation_result = await self.system_coordinator.run_comprehensive_ai_validation()

            success_rate = validation_result.get('success_rate', 0.0)
            validation_status = validation_result.get('overall_status', 'UNKNOWN')

            logger.info(f"AI validation: {success_rate:.1%} success rate ({validation_status})")

            if success_rate < 0.7:
                logger.error("AI validation failed")
                return False

            logger.info("✅ Performance validation passed")
            return True

        except Exception as e:
            logger.error(f"Performance validation test failed: {e}")
            return False

    async def _test_recovery_mechanisms(self) -> bool:
        """Test system recovery mechanisms"""
        try:
            logger.info("Testing recovery mechanisms...")

            # Test pause and resume functionality
            logger.info("Testing pause functionality...")
            pause_result = await self.system_coordinator.pause()

            if not pause_result:
                logger.error("System pause failed")
                return False

            logger.info("✅ System paused successfully")

            # Wait a moment
            await asyncio.sleep(2)

            # Test resume functionality
            logger.info("Testing resume functionality...")
            resume_result = await self.system_coordinator.resume()

            if not resume_result:
                logger.error("System resume failed")
                return False

            logger.info("✅ System resumed successfully")

            # Verify system is back to running state
            status = await self.system_coordinator.get_system_status()
            if status.state.value != 'running':
                logger.error(f"System not back to running state: {status.state.value}")
                return False

            logger.info("✅ Recovery mechanisms test passed")
            return True

        except Exception as e:
            logger.error(f"Recovery mechanisms test failed: {e}")
            return False

    async def _test_complete_system_validation(self) -> bool:
        """Test complete system validation"""
        try:
            logger.info("Running complete system validation...")

            # Test AI performance optimization
            optimization_result = await self.system_coordinator.optimize_ai_performance()

            if optimization_result.get('success', False):
                logger.info("✅ AI performance optimization passed")
            else:
                logger.warning("⚠️ AI performance optimization had issues")

            # Final system health check
            final_health = await self.system_coordinator.get_comprehensive_health_report()
            final_health_score = final_health.get('overall_health', 0.0)

            logger.info(f"Final system health: {final_health_score:.1%}")

            # Final AI integration status
            final_ai_status = await self.system_coordinator.get_ai_integration_status()
            ai_active = final_ai_status.get('ai_integration_active', False)

            logger.info(f"Final AI integration status: {'ACTIVE' if ai_active else 'INACTIVE'}")

            # System must be healthy and AI must be active
            return final_health_score >= 0.7 and ai_active

        except Exception as e:
            logger.error(f"Complete system validation failed: {e}")
            return False

    async def _generate_final_report(self) -> Dict[str, Any]:
        """Generate final test report"""
        try:
            total_duration = time.time() - self.start_time

            # Calculate overall results
            passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
            total_tests = len(self.test_results)
            overall_success_rate = passed_tests / total_tests if total_tests > 0 else 0

            # Determine overall status
            if overall_success_rate >= 0.9:
                overall_status = 'EXCELLENT'
            elif overall_success_rate >= 0.8:
                overall_status = 'GOOD'
            elif overall_success_rate >= 0.7:
                overall_status = 'ACCEPTABLE'
            elif overall_success_rate >= 0.5:
                overall_status = 'POOR'
            else:
                overall_status = 'CRITICAL'

            # Get final system state
            final_system_status = None
            final_ai_status = None

            if self.system_coordinator:
                try:
                    final_system_status = await self.system_coordinator.get_system_status()
                    final_ai_status = await self.system_coordinator.get_ai_integration_status()
                except:
                    pass

            # Compile final report
            final_report = {
                'test_timestamp': datetime.now().isoformat(),
                'total_duration': total_duration,
                'overall_status': overall_status,
                'overall_success_rate': overall_success_rate,
                'passed_tests': passed_tests,
                'total_tests': total_tests,
                'test_results': self.test_results,
                'final_system_state': final_system_status.state.value if final_system_status else 'unknown',
                'final_ai_integration': final_ai_status.get('ai_integration_active', False) if final_ai_status else False,
                'system_metrics': {
                    'system_health': final_system_status.system_health if final_system_status else 0.0,
                    'active_agents': final_system_status.active_agents if final_system_status else 0,
                    'active_strategies': final_system_status.active_strategies if final_system_status else 0,
                    'uptime': final_system_status.uptime if final_system_status else 0.0
                }
            }

            # Log final summary
            logger.info(f"\n{'='*80}")
            logger.info(f"🏁 COMPLETE AI SYSTEM INTEGRATION TEST RESULTS")
            logger.info(f"{'='*80}")
            logger.info(f"Overall Status: {overall_status}")
            logger.info(f"Success Rate: {overall_success_rate:.1%} ({passed_tests}/{total_tests})")
            logger.info(f"Total Duration: {total_duration:.2f}s")
            logger.info(f"Final System State: {final_report['final_system_state']}")
            logger.info(f"AI Integration Active: {final_report['final_ai_integration']}")

            if final_system_status:
                logger.info(f"System Health: {final_system_status.system_health:.1%}")
                logger.info(f"Active Agents: {final_system_status.active_agents}")
                logger.info(f"Active Strategies: {final_system_status.active_strategies}")

            # Log failed tests
            failed_tests = [name for name, result in self.test_results.items() if not result.get('success', False)]
            if failed_tests:
                logger.warning(f"Failed Tests: {', '.join(failed_tests)}")

            logger.info(f"{'='*80}")

            return final_report

        except Exception as e:
            logger.error(f"Error generating final report: {e}")
            return {
                'test_timestamp': datetime.now().isoformat(),
                'overall_status': 'ERROR',
                'error': str(e)
            }


async def main():
    """Main test execution"""
    try:
        # Create and run test suite
        test_suite = CompleteAISystemIntegrationTest()
        results = await test_suite.run_complete_test_suite()

        # Save results to file
        with open(f'ai_system_integration_test_results_{int(time.time())}.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)

        # Print summary
        print(f"\n🎯 Test completed with {results.get('overall_status', 'UNKNOWN')} status")
        print(f"📊 Success rate: {results.get('overall_success_rate', 0):.1%}")
        print(f"⏱️ Duration: {results.get('total_duration', 0):.2f}s")

        return results.get('overall_success_rate', 0) >= 0.8

    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
