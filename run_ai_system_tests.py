#!/usr/bin/env python3
"""
AI System Test Runner
Simple script to run various AI system tests
"""

import asyncio
import argparse
import logging
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def run_basic_system_test():
    """Run basic system functionality test"""
    try:
        logger.info("🧪 Running Basic System Test...")
        
        from system.system_coordinator import SystemCoordinator
        
        # Initialize system
        coordinator = SystemCoordinator('config/system_config.yaml')
        
        # Test initialization
        init_success = await coordinator.initialize()
        if not init_success:
            logger.error("❌ System initialization failed")
            return False
            
        logger.info("✅ System initialized")
        
        # Test start
        start_success = await coordinator.start()
        if not start_success:
            logger.error("❌ System start failed")
            return False
            
        logger.info("✅ System started")
        
        # Test system status
        status = await coordinator.get_system_status()
        logger.info(f"✅ System status: {status.state.value}")
        
        # Test stop
        stop_success = await coordinator.stop()
        if not stop_success:
            logger.error("❌ System stop failed")
            return False
            
        logger.info("✅ System stopped")
        return True
        
    except Exception as e:
        logger.error(f"❌ Basic system test failed: {e}")
        return False


async def run_ai_integration_test():
    """Run AI integration test"""
    try:
        logger.info("🤖 Running AI Integration Test...")
        
        from system.system_coordinator import SystemCoordinator
        
        # Initialize system
        coordinator = SystemCoordinator('config/system_config.yaml')
        
        # Initialize and start
        await coordinator.initialize()
        await coordinator.start()
        
        # Test AI integration activation
        ai_result = await coordinator.activate_ai_integration()
        
        if not ai_result.get('success', False):
            logger.error("❌ AI integration activation failed")
            return False
            
        logger.info("✅ AI integration activated")
        
        # Test AI validation
        validation_result = await coordinator.run_comprehensive_ai_validation()
        success_rate = validation_result.get('success_rate', 0.0)
        
        logger.info(f"✅ AI validation success rate: {success_rate:.1%}")
        
        # Cleanup
        await coordinator.stop()
        
        return success_rate >= 0.7
        
    except Exception as e:
        logger.error(f"❌ AI integration test failed: {e}")
        return False


async def run_component_health_test():
    """Run component health test"""
    try:
        logger.info("🏥 Running Component Health Test...")
        
        from system.system_coordinator import SystemCoordinator
        
        # Initialize system
        coordinator = SystemCoordinator('config/system_config.yaml')
        
        # Initialize and start
        await coordinator.initialize()
        await coordinator.start()
        
        # Get health report
        health_report = await coordinator.get_comprehensive_health_report()
        
        overall_health = health_report.get('overall_health', 0.0)
        health_status = health_report.get('health_status', 'unknown')
        
        logger.info(f"✅ System health: {overall_health:.1%} ({health_status})")
        
        # Check component health
        component_health = health_report.get('component_health', {})
        for component_name, health_info in component_health.items():
            status = "✅" if health_info.get('running', False) else "❌"
            logger.info(f"{status} {component_name}: {health_info.get('status', 'unknown')}")
        
        # Cleanup
        await coordinator.stop()
        
        return overall_health >= 0.7
        
    except Exception as e:
        logger.error(f"❌ Component health test failed: {e}")
        return False


async def run_complete_integration_test():
    """Run complete integration test"""
    try:
        logger.info("🚀 Running Complete Integration Test...")
        
        from test_complete_ai_system_integration import CompleteAISystemIntegrationTest
        
        # Run complete test suite
        test_suite = CompleteAISystemIntegrationTest()
        results = await test_suite.run_complete_test_suite()
        
        overall_status = results.get('overall_status', 'UNKNOWN')
        success_rate = results.get('overall_success_rate', 0.0)
        
        logger.info(f"✅ Complete integration test: {overall_status} ({success_rate:.1%})")
        
        return success_rate >= 0.8
        
    except Exception as e:
        logger.error(f"❌ Complete integration test failed: {e}")
        return False


async def run_ollama_connectivity_test():
    """Run Ollama connectivity test"""
    try:
        logger.info("🔗 Running Ollama Connectivity Test...")
        
        from models.ollama_hub import OllamaModelHub
        
        # Initialize Ollama hub
        ollama_hub = OllamaModelHub()
        await ollama_hub.initialize()
        
        # Test model availability
        models = await ollama_hub.get_available_models()
        logger.info(f"✅ Found {len(models)} available models")
        
        if len(models) == 0:
            logger.error("❌ No models available")
            return False
        
        # Test model deployment
        test_instance = await ollama_hub.deploy_model_for_agent(
            agent_name="test_connectivity",
            role="test"
        )
        
        if test_instance:
            logger.info("✅ Model deployment test passed")
            
            # Cleanup
            await ollama_hub.deployment_manager.undeploy_model("test_connectivity")
            return True
        else:
            logger.error("❌ Model deployment test failed")
            return False
        
    except Exception as e:
        logger.error(f"❌ Ollama connectivity test failed: {e}")
        return False


def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description='AI System Test Runner')
    parser.add_argument('--test', choices=[
        'basic', 'ai', 'health', 'complete', 'ollama', 'all'
    ], default='all', help='Test type to run')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Define test functions
    test_functions = {
        'basic': run_basic_system_test,
        'ai': run_ai_integration_test,
        'health': run_component_health_test,
        'complete': run_complete_integration_test,
        'ollama': run_ollama_connectivity_test
    }
    
    async def run_tests():
        """Run selected tests"""
        results = {}
        
        if args.test == 'all':
            tests_to_run = test_functions.items()
        else:
            tests_to_run = [(args.test, test_functions[args.test])]
        
        logger.info(f"🎯 Starting test run at {datetime.now().isoformat()}")
        
        for test_name, test_function in tests_to_run:
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"Running {test_name} test...")
                logger.info(f"{'='*50}")
                
                result = await test_function()
                results[test_name] = result
                
                status = "✅ PASSED" if result else "❌ FAILED"
                logger.info(f"{status}: {test_name} test")
                
            except Exception as e:
                logger.error(f"❌ {test_name} test error: {e}")
                results[test_name] = False
        
        # Summary
        passed_tests = sum(1 for result in results.values() if result)
        total_tests = len(results)
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        logger.info(f"\n{'='*60}")
        logger.info(f"🏁 TEST SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Success Rate: {success_rate:.1%} ({passed_tests}/{total_tests})")
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        
        logger.info(f"{'='*60}")
        
        return success_rate >= 0.8
    
    try:
        success = asyncio.run(run_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Test run interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test run failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
