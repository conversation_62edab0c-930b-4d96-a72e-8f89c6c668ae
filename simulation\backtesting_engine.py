"""
Advanced Backtesting Engine for AI Trading Strategies
Provides comprehensive strategy testing, validation, and performance analysis
"""

import asyncio
import logging
import time
import math
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class BacktestStatus(Enum):
    """Backtest execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    strategy_id: str
    agent_id: str
    start_date: datetime
    end_date: datetime
    initial_capital: float
    symbols: List[str]
    benchmark: str = "SPY"
    commission: float = 0.001  # 0.1%
    slippage: float = 0.0005   # 0.05%
    max_positions: int = 10
    rebalance_frequency: str = "daily"  # daily, weekly, monthly
    risk_free_rate: float = 0.02  # 2%
    created_at: float = field(default_factory=time.time)


@dataclass
class Trade:
    """Individual trade record"""
    timestamp: datetime
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float
    price: float
    commission: float
    slippage: float
    total_cost: float
    agent_id: str
    strategy_id: str
    reason: str = ""


@dataclass
class Position:
    """Position holding"""
    symbol: str
    quantity: float
    avg_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    first_trade_date: datetime
    last_trade_date: datetime


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    avg_trade_duration: float
    benchmark_return: float
    alpha: float
    beta: float
    information_ratio: float
    var_95: float  # 95% Value at Risk
    cvar_95: float  # 95% Conditional Value at Risk


@dataclass
class BacktestResult:
    """Complete backtest results"""
    backtest_id: str
    config: BacktestConfig
    status: BacktestStatus
    start_time: datetime
    end_time: Optional[datetime]
    duration_seconds: float
    final_portfolio_value: float
    trades: List[Trade]
    positions: List[Position]
    daily_returns: List[float]
    daily_portfolio_values: List[float]
    performance_metrics: PerformanceMetrics
    risk_metrics: Dict[str, float]
    error_message: Optional[str] = None


class BacktestingEngine:
    """
    Advanced Backtesting Engine for AI Trading Strategies
    
    Features:
    - Historical data simulation
    - Realistic transaction costs
    - Multiple asset support
    - Performance analytics
    - Risk-adjusted metrics
    - Benchmark comparison
    - Strategy validation
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Backtest storage
        self.active_backtests: Dict[str, BacktestResult] = {}
        self.completed_backtests: Dict[str, BacktestResult] = {}
        
        # Market data
        self.historical_data: Dict[str, pd.DataFrame] = {}
        self.benchmark_data: Dict[str, pd.DataFrame] = {}
        
        # State
        self.initialized = False
        
        # Configuration
        self.max_concurrent_backtests = config.get('backtesting', {}).get('max_concurrent', 5)
        self.data_frequency = config.get('backtesting', {}).get('data_frequency', 'daily')
        
    async def initialize(self):
        """Initialize backtesting engine"""
        try:
            logger.info("Initializing Backtesting Engine...")
            
            # Load historical market data
            await self._load_historical_data()
            
            # Setup benchmark data
            await self._load_benchmark_data()
            
            self.initialized = True
            logger.info("✓ Backtesting Engine initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Backtesting Engine: {e}")
            raise
            
    async def run_backtest(self, config: BacktestConfig) -> str:
        """Run a backtest with given configuration"""
        if not self.initialized:
            await self.initialize()
            
        # Check concurrent limit
        if len(self.active_backtests) >= self.max_concurrent_backtests:
            raise RuntimeError(f"Maximum concurrent backtests ({self.max_concurrent_backtests}) reached")
            
        # Generate backtest ID
        backtest_id = f"bt_{config.strategy_id}_{int(time.time())}"
        
        # Create backtest result
        result = BacktestResult(
            backtest_id=backtest_id,
            config=config,
            status=BacktestStatus.PENDING,
            start_time=datetime.now(),
            end_time=None,
            duration_seconds=0.0,
            final_portfolio_value=config.initial_capital,
            trades=[],
            positions=[],
            daily_returns=[],
            daily_portfolio_values=[],
            performance_metrics=PerformanceMetrics(
                total_return=0.0, annualized_return=0.0, volatility=0.0,
                sharpe_ratio=0.0, sortino_ratio=0.0, max_drawdown=0.0,
                calmar_ratio=0.0, win_rate=0.0, profit_factor=0.0,
                total_trades=0, winning_trades=0, losing_trades=0,
                avg_win=0.0, avg_loss=0.0, largest_win=0.0, largest_loss=0.0,
                avg_trade_duration=0.0, benchmark_return=0.0, alpha=0.0,
                beta=0.0, information_ratio=0.0, var_95=0.0, cvar_95=0.0
            ),
            risk_metrics={}
        )
        
        # Store and start backtest
        self.active_backtests[backtest_id] = result
        
        # Run backtest asynchronously
        asyncio.create_task(self._execute_backtest(backtest_id))
        
        logger.info(f"Started backtest {backtest_id} for strategy {config.strategy_id}")
        return backtest_id
        
    async def _execute_backtest(self, backtest_id: str):
        """Execute the actual backtest"""
        result = self.active_backtests[backtest_id]
        config = result.config
        
        try:
            result.status = BacktestStatus.RUNNING
            start_time = time.time()
            
            # Initialize portfolio
            portfolio = {
                'cash': config.initial_capital,
                'positions': {},
                'total_value': config.initial_capital
            }
            
            # Get date range
            current_date = config.start_date
            end_date = config.end_date
            
            # Daily simulation loop
            while current_date <= end_date:
                # Get market data for current date
                market_data = await self._get_market_data_for_date(current_date, config.symbols)
                
                if market_data:
                    # Update portfolio values
                    await self._update_portfolio_values(portfolio, market_data)
                    
                    # Record daily portfolio value
                    result.daily_portfolio_values.append(portfolio['total_value'])
                    
                    # Calculate daily return
                    if len(result.daily_portfolio_values) > 1:
                        prev_value = result.daily_portfolio_values[-2]
                        daily_return = (portfolio['total_value'] - prev_value) / prev_value
                        result.daily_returns.append(daily_return)
                    
                    # Simulate AI agent decision making
                    trades = await self._simulate_agent_decisions(
                        config.agent_id, current_date, market_data, portfolio
                    )
                    
                    # Execute trades
                    for trade_signal in trades:
                        trade = await self._execute_trade(trade_signal, market_data, portfolio, config)
                        if trade:
                            result.trades.append(trade)
                
                # Move to next day
                current_date += timedelta(days=1)
                
                # Small delay to prevent blocking
                if len(result.daily_portfolio_values) % 100 == 0:
                    await asyncio.sleep(0.001)
                    
            # Calculate final metrics
            result.final_portfolio_value = portfolio['total_value']
            result.performance_metrics = await self._calculate_performance_metrics(result)
            result.risk_metrics = await self._calculate_risk_metrics(result)
            
            # Update positions
            result.positions = [
                Position(
                    symbol=symbol,
                    quantity=pos['quantity'],
                    avg_price=pos['avg_price'],
                    current_price=market_data.get(symbol, {}).get('close', pos['avg_price']),
                    market_value=pos['quantity'] * market_data.get(symbol, {}).get('close', pos['avg_price']),
                    unrealized_pnl=pos.get('unrealized_pnl', 0.0),
                    realized_pnl=pos.get('realized_pnl', 0.0),
                    first_trade_date=pos.get('first_trade_date', config.start_date),
                    last_trade_date=pos.get('last_trade_date', config.end_date)
                )
                for symbol, pos in portfolio['positions'].items()
                if pos['quantity'] != 0
            ]
            
            # Complete backtest
            result.status = BacktestStatus.COMPLETED
            result.end_time = datetime.now()
            result.duration_seconds = time.time() - start_time
            
            # Move to completed
            self.completed_backtests[backtest_id] = result
            del self.active_backtests[backtest_id]
            
            logger.info(f"✓ Backtest {backtest_id} completed in {result.duration_seconds:.2f}s")
            
        except Exception as e:
            result.status = BacktestStatus.FAILED
            result.error_message = str(e)
            result.end_time = datetime.now()
            
            # Move to completed (even if failed)
            self.completed_backtests[backtest_id] = result
            if backtest_id in self.active_backtests:
                del self.active_backtests[backtest_id]
                
            logger.error(f"❌ Backtest {backtest_id} failed: {e}")
            
    async def _load_historical_data(self):
        """Load historical market data"""
        # This would load real historical data
        # For simulation, we'll generate realistic data
        symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'NVDA', 'META', 'SPY']
        
        for symbol in symbols:
            # Generate realistic price data
            data = await self._generate_realistic_price_data(symbol, 1000)  # 1000 days
            self.historical_data[symbol] = data
            
    async def _load_benchmark_data(self):
        """Load benchmark data"""
        # Load SPY as default benchmark
        if 'SPY' in self.historical_data:
            self.benchmark_data['SPY'] = self.historical_data['SPY']
            
    async def _generate_realistic_price_data(self, symbol: str, days: int) -> pd.DataFrame:
        """Generate realistic price data for simulation"""
        # This creates realistic price movements
        np.random.seed(hash(symbol) % 2**32)  # Consistent data per symbol
        
        # Base parameters
        initial_price = np.random.uniform(50, 300)
        annual_return = np.random.uniform(-0.1, 0.15)  # -10% to 15%
        volatility = np.random.uniform(0.15, 0.4)      # 15% to 40%
        
        # Generate returns
        daily_return = annual_return / 252
        daily_vol = volatility / np.sqrt(252)
        
        returns = np.random.normal(daily_return, daily_vol, days)
        prices = [initial_price]
        
        for ret in returns:
            prices.append(prices[-1] * (1 + ret))
            
        # Create DataFrame with consistent lengths
        dates = pd.date_range(start='2020-01-01', periods=days, freq='D')

        df = pd.DataFrame({
            'date': dates,
            'open': prices[:-1],
            'high': [p * np.random.uniform(1.0, 1.02) for p in prices[:-1]],
            'low': [p * np.random.uniform(0.98, 1.0) for p in prices[:-1]],
            'close': prices[1:],
            'volume': [np.random.randint(1000000, 10000000) for _ in range(days)]
        })
        
        return df

    async def _get_market_data_for_date(self, date, symbols):
        """Get market data for a specific date"""
        market_data = {}
        for symbol in symbols:
            if symbol in self.historical_data:
                df = self.historical_data[symbol]
                # Find data for the date (simplified)
                if len(df) > 0:
                    idx = min(len(df) - 1, int((date.timestamp() - pd.Timestamp('2020-01-01').timestamp()) / 86400))
                    if 0 <= idx < len(df):
                        row = df.iloc[idx]
                        market_data[symbol] = {
                            'open': row['open'],
                            'high': row['high'],
                            'low': row['low'],
                            'close': row['close'],
                            'volume': row['volume']
                        }
        return market_data

    async def _update_portfolio_values(self, portfolio, market_data):
        """Update portfolio values based on market data"""
        total_value = portfolio['cash']

        for symbol, position in portfolio['positions'].items():
            if symbol in market_data:
                current_price = market_data[symbol]['close']
                position_value = position['quantity'] * current_price
                total_value += position_value

                # Update unrealized P&L
                cost_basis = position['quantity'] * position['avg_price']
                position['unrealized_pnl'] = position_value - cost_basis

        portfolio['total_value'] = total_value

    async def _simulate_agent_decisions(self, agent_id, current_date, market_data, portfolio):
        """Simulate AI agent trading decisions"""
        # This is a simplified simulation of agent decisions
        trades = []

        # Random trading decisions for simulation
        if random.random() < 0.1:  # 10% chance of trading each day
            symbols = list(market_data.keys())
            if symbols:
                symbol = random.choice(symbols)
                side = random.choice(['buy', 'sell'])

                # Position sizing
                if side == 'buy':
                    max_position_value = portfolio['total_value'] * 0.1  # 10% max position
                    price = market_data[symbol]['close']
                    quantity = max_position_value / price

                    if quantity >= 1 and portfolio['cash'] >= quantity * price:
                        trades.append({
                            'symbol': symbol,
                            'side': side,
                            'quantity': quantity,
                            'price': price
                        })

                elif side == 'sell' and symbol in portfolio['positions']:
                    position = portfolio['positions'][symbol]
                    if position['quantity'] > 0:
                        quantity = min(position['quantity'], position['quantity'] * 0.5)  # Sell up to 50%
                        price = market_data[symbol]['close']

                        trades.append({
                            'symbol': symbol,
                            'side': side,
                            'quantity': quantity,
                            'price': price
                        })

        return trades

    async def _execute_trade(self, trade_signal, market_data, portfolio, config):
        """Execute a trade in the backtest"""
        symbol = trade_signal['symbol']
        side = trade_signal['side']
        quantity = trade_signal['quantity']
        price = trade_signal['price']

        # Calculate commission and slippage
        trade_value = quantity * price
        commission = max(trade_value * config.commission, 1.0)  # Min $1 commission
        slippage = trade_value * config.slippage_factor

        if side == 'buy':
            total_cost = trade_value + commission + slippage
            if portfolio['cash'] >= total_cost:
                # Execute buy
                portfolio['cash'] -= total_cost

                if symbol not in portfolio['positions']:
                    portfolio['positions'][symbol] = {
                        'quantity': 0,
                        'avg_price': 0,
                        'realized_pnl': 0,
                        'first_trade_date': config.start_date,
                        'last_trade_date': config.start_date
                    }

                position = portfolio['positions'][symbol]
                total_quantity = position['quantity'] + quantity
                total_cost_basis = (position['quantity'] * position['avg_price']) + trade_value
                position['avg_price'] = total_cost_basis / total_quantity
                position['quantity'] = total_quantity

                return Trade(
                    timestamp=config.start_date,
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    price=price,
                    commission=commission,
                    slippage=slippage,
                    total_cost=total_cost,
                    agent_id=config.agent_id,
                    strategy_id=config.strategy_id
                )

        elif side == 'sell' and symbol in portfolio['positions']:
            position = portfolio['positions'][symbol]
            if position['quantity'] >= quantity:
                # Execute sell
                proceeds = trade_value - commission - slippage
                portfolio['cash'] += proceeds

                # Calculate realized P&L
                cost_basis = quantity * position['avg_price']
                realized_pnl = proceeds - cost_basis
                position['realized_pnl'] += realized_pnl

                # Update position
                position['quantity'] -= quantity

                # Remove position if quantity is zero
                if position['quantity'] <= 0:
                    del portfolio['positions'][symbol]

                return Trade(
                    timestamp=config.start_date,
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    price=price,
                    commission=commission,
                    slippage=slippage,
                    total_cost=proceeds,
                    agent_id=config.agent_id,
                    strategy_id=config.strategy_id
                )

        return None

    async def _calculate_performance_metrics(self, result):
        """Calculate comprehensive performance metrics"""
        if not result.daily_returns:
            return result.performance_metrics

        returns = np.array(result.daily_returns)

        # Basic metrics
        total_return = (result.final_portfolio_value - result.config.initial_capital) / result.config.initial_capital
        trading_days = len(returns)
        annualized_return = (1 + total_return) ** (252 / trading_days) - 1 if trading_days > 0 else 0

        # Risk metrics
        volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0

        # Sharpe ratio
        risk_free_rate = 0.02  # 2%
        excess_returns = returns - (risk_free_rate / 252)
        sharpe_ratio = np.mean(excess_returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0

        # Max drawdown
        portfolio_values = result.daily_portfolio_values
        if len(portfolio_values) > 1:
            peak = np.maximum.accumulate(portfolio_values)
            drawdown = (np.array(portfolio_values) - peak) / peak
            max_drawdown = abs(np.min(drawdown))
        else:
            max_drawdown = 0

        # Trade analysis
        winning_trades = len([t for t in result.trades if t.total_cost > 0])  # Simplified
        total_trades = len(result.trades)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # Update performance metrics
        result.performance_metrics.total_return = total_return
        result.performance_metrics.annualized_return = annualized_return
        result.performance_metrics.volatility = volatility
        result.performance_metrics.sharpe_ratio = sharpe_ratio
        result.performance_metrics.max_drawdown = max_drawdown
        result.performance_metrics.win_rate = win_rate
        result.performance_metrics.total_trades = total_trades
        result.performance_metrics.winning_trades = winning_trades
        result.performance_metrics.losing_trades = total_trades - winning_trades

        return result.performance_metrics

    async def _calculate_risk_metrics(self, result):
        """Calculate risk metrics"""
        return {
            'var_95': np.percentile(result.daily_returns, 5) if result.daily_returns else 0,
            'volatility': np.std(result.daily_returns) * np.sqrt(252) if result.daily_returns else 0,
            'max_drawdown': result.performance_metrics.max_drawdown
        }
