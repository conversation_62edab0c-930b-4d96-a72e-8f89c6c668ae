"""
Performance Analytics Dashboard - Comprehensive Analytics for AI Trading Simulation
Provides detailed performance metrics, visualizations, and insights
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
import numpy as np
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class AnalyticsTimeframe(Enum):
    """Analytics timeframes"""
    INTRADAY = "intraday"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


@dataclass
class PerformanceReport:
    """Comprehensive performance report"""
    agent_id: str
    timeframe: AnalyticsTimeframe
    start_date: datetime
    end_date: datetime
    
    # Portfolio metrics
    initial_value: float
    final_value: float
    total_return: float
    annualized_return: float
    
    # Risk metrics
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    var_95: float
    
    # Trading metrics
    total_trades: int
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    
    # Benchmark comparison
    benchmark_return: float
    alpha: float
    beta: float
    information_ratio: float
    
    # Additional insights
    best_trade: Dict[str, Any]
    worst_trade: Dict[str, Any]
    sector_performance: Dict[str, float]
    monthly_returns: List[float]


@dataclass
class RiskReport:
    """Risk analysis report"""
    agent_id: str
    timestamp: float
    
    # Position risk
    position_concentration: Dict[str, float]
    sector_concentration: Dict[str, float]
    largest_position_pct: float
    
    # Market risk
    portfolio_beta: float
    correlation_risk: float
    volatility_risk: float
    
    # Liquidity risk
    liquidity_score: float
    days_to_liquidate: float
    
    # Risk limits
    risk_limit_breaches: List[Dict[str, Any]]
    risk_warnings: List[str]


class PerformanceAnalytics:
    """
    Comprehensive Performance Analytics System
    
    Features:
    - Real-time performance tracking
    - Risk analysis and reporting
    - Benchmark comparison
    - Trade analysis
    - Portfolio attribution
    - Custom dashboards
    """
    
    def __init__(self, config: Dict[str, Any], market_simulator, ai_integration):
        self.config = config
        self.market_simulator = market_simulator
        self.ai_integration = ai_integration
        
        # Analytics storage
        self.performance_reports: Dict[str, List[PerformanceReport]] = {}
        self.risk_reports: Dict[str, List[RiskReport]] = {}
        self.daily_snapshots: Dict[str, List[Dict[str, Any]]] = {}
        
        # Benchmark data
        self.benchmark_data: List[Tuple[float, float]] = []  # (timestamp, value)
        self.benchmark_symbol = config.get('analytics', {}).get('benchmark', 'SPY')
        
        # State
        self.initialized = False
        self.running = False
        self.analytics_task: Optional[asyncio.Task] = None
        
        # Configuration
        self.report_interval = config.get('analytics', {}).get('report_interval', 3600)  # 1 hour
        self.snapshot_interval = config.get('analytics', {}).get('snapshot_interval', 300)  # 5 minutes
        
    async def initialize(self):
        """Initialize performance analytics"""
        try:
            logger.info("Initializing Performance Analytics...")
            
            # Load benchmark data
            await self._load_benchmark_data()
            
            self.initialized = True
            logger.info("✓ Performance Analytics initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Performance Analytics: {e}")
            raise
            
    async def start(self):
        """Start performance analytics"""
        if not self.initialized:
            await self.initialize()
            
        self.running = True
        self.analytics_task = asyncio.create_task(self._analytics_loop())
        logger.info("✓ Performance Analytics started")
        
    async def stop(self):
        """Stop performance analytics"""
        self.running = False
        if self.analytics_task:
            self.analytics_task.cancel()
            try:
                await self.analytics_task
            except asyncio.CancelledError:
                pass
        logger.info("✓ Performance Analytics stopped")
        
    async def generate_performance_report(self, agent_id: str, 
                                        timeframe: AnalyticsTimeframe = AnalyticsTimeframe.DAILY) -> PerformanceReport:
        """Generate comprehensive performance report"""
        
        # Get portfolio data
        portfolio = await self.ai_integration.get_agent_portfolio(agent_id)
        if not portfolio:
            raise ValueError(f"No portfolio data for agent {agent_id}")
            
        # Calculate timeframe dates
        end_date = datetime.now()
        if timeframe == AnalyticsTimeframe.DAILY:
            start_date = end_date - timedelta(days=1)
        elif timeframe == AnalyticsTimeframe.WEEKLY:
            start_date = end_date - timedelta(weeks=1)
        elif timeframe == AnalyticsTimeframe.MONTHLY:
            start_date = end_date - timedelta(days=30)
        else:
            start_date = end_date - timedelta(days=7)  # Default to weekly
            
        # Get historical snapshots
        snapshots = self._get_snapshots_for_period(agent_id, start_date, end_date)
        
        if len(snapshots) < 2:
            raise ValueError("Insufficient data for performance calculation")
            
        # Calculate performance metrics
        initial_value = snapshots[0]['total_value']
        final_value = snapshots[-1]['total_value']
        total_return = (final_value - initial_value) / initial_value if initial_value > 0 else 0
        
        # Calculate daily returns
        daily_returns = []
        for i in range(1, len(snapshots)):
            prev_value = snapshots[i-1]['total_value']
            curr_value = snapshots[i]['total_value']
            if prev_value > 0:
                daily_return = (curr_value - prev_value) / prev_value
                daily_returns.append(daily_return)
                
        # Risk metrics
        returns_array = np.array(daily_returns) if daily_returns else np.array([0])
        volatility = np.std(returns_array) * np.sqrt(252) if len(returns_array) > 1 else 0
        
        # Sharpe ratio
        risk_free_rate = self.config.get('analytics', {}).get('risk_free_rate', 0.02)
        excess_returns = returns_array - (risk_free_rate / 252)
        sharpe_ratio = np.mean(excess_returns) / np.std(returns_array) * np.sqrt(252) if np.std(returns_array) > 0 else 0
        
        # Sortino ratio
        downside_returns = returns_array[returns_array < 0]
        downside_deviation = np.std(downside_returns) if len(downside_returns) > 0 else 0
        sortino_ratio = np.mean(excess_returns) / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0
        
        # Max drawdown
        portfolio_values = [s['total_value'] for s in snapshots]
        max_drawdown = await self._calculate_max_drawdown(portfolio_values)
        
        # VaR
        var_95 = np.percentile(returns_array, 5) if len(returns_array) > 0 else 0
        
        # Trading metrics
        performance = await self.ai_integration.get_agent_performance(agent_id)
        
        # Benchmark comparison
        benchmark_return = await self._calculate_benchmark_return(start_date, end_date)
        beta, alpha = await self._calculate_beta_alpha(returns_array, start_date, end_date)
        
        # Information ratio
        tracking_error = np.std(returns_array - benchmark_return) if len(returns_array) > 0 else 0
        information_ratio = (total_return - benchmark_return) / tracking_error if tracking_error > 0 else 0
        
        # Best/worst trades
        best_trade, worst_trade = await self._find_best_worst_trades(agent_id)
        
        # Sector performance
        sector_performance = await self._calculate_sector_performance(portfolio)
        
        # Monthly returns
        monthly_returns = await self._calculate_monthly_returns(snapshots)
        
        report = PerformanceReport(
            agent_id=agent_id,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            initial_value=initial_value,
            final_value=final_value,
            total_return=total_return,
            annualized_return=(1 + total_return) ** (365 / (end_date - start_date).days) - 1,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            var_95=var_95,
            total_trades=performance.total_trades,
            win_rate=performance.win_rate,
            profit_factor=performance.avg_win / abs(performance.avg_loss) if performance.avg_loss != 0 else 0,
            avg_win=performance.avg_win,
            avg_loss=performance.avg_loss,
            benchmark_return=benchmark_return,
            alpha=alpha,
            beta=beta,
            information_ratio=information_ratio,
            best_trade=best_trade,
            worst_trade=worst_trade,
            sector_performance=sector_performance,
            monthly_returns=monthly_returns
        )
        
        # Store report
        if agent_id not in self.performance_reports:
            self.performance_reports[agent_id] = []
        self.performance_reports[agent_id].append(report)
        
        return report
        
    async def generate_risk_report(self, agent_id: str) -> RiskReport:
        """Generate comprehensive risk report"""
        
        portfolio = await self.ai_integration.get_agent_portfolio(agent_id)
        if not portfolio:
            raise ValueError(f"No portfolio data for agent {agent_id}")
            
        # Position concentration
        positions = portfolio.get('positions', {})
        total_value = portfolio.get('total_value', 0)
        
        position_concentration = {}
        sector_concentration = {}
        largest_position_pct = 0
        
        if total_value > 0:
            for symbol, position in positions.items():
                position_pct = position['market_value'] / total_value
                position_concentration[symbol] = position_pct
                largest_position_pct = max(largest_position_pct, position_pct)
                
                # Sector allocation (simplified)
                sector = self._get_symbol_sector(symbol)
                if sector not in sector_concentration:
                    sector_concentration[sector] = 0
                sector_concentration[sector] += position_pct
                
        # Risk metrics calculations (simplified)
        portfolio_beta = 1.0  # Would calculate from actual correlations
        correlation_risk = 0.5  # Would calculate from position correlations
        volatility_risk = 0.3  # Would calculate from portfolio volatility
        liquidity_score = 0.8  # Would calculate from position sizes and volumes
        days_to_liquidate = 2.0  # Would calculate from position sizes and average volumes
        
        # Risk limit breaches (would check against actual limits)
        risk_limit_breaches = []
        risk_warnings = []
        
        if largest_position_pct > 0.2:  # 20% limit
            risk_warnings.append(f"Largest position {largest_position_pct:.1%} exceeds 20% limit")
            
        report = RiskReport(
            agent_id=agent_id,
            timestamp=time.time(),
            position_concentration=position_concentration,
            sector_concentration=sector_concentration,
            largest_position_pct=largest_position_pct,
            portfolio_beta=portfolio_beta,
            correlation_risk=correlation_risk,
            volatility_risk=volatility_risk,
            liquidity_score=liquidity_score,
            days_to_liquidate=days_to_liquidate,
            risk_limit_breaches=risk_limit_breaches,
            risk_warnings=risk_warnings
        )
        
        # Store report
        if agent_id not in self.risk_reports:
            self.risk_reports[agent_id] = []
        self.risk_reports[agent_id].append(report)
        
        return report
        
    async def get_dashboard_data(self, agent_id: str) -> Dict[str, Any]:
        """Get comprehensive dashboard data for an agent"""
        
        # Get latest portfolio
        portfolio = await self.ai_integration.get_agent_portfolio(agent_id)
        
        # Get latest performance report
        try:
            performance_report = await self.generate_performance_report(agent_id, AnalyticsTimeframe.DAILY)
        except:
            performance_report = None
            
        # Get latest risk report
        try:
            risk_report = await self.generate_risk_report(agent_id)
        except:
            risk_report = None
            
        # Get recent snapshots for charts
        recent_snapshots = self.daily_snapshots.get(agent_id, [])[-100:]  # Last 100 snapshots
        
        # Market status
        market_status = await self.market_simulator.get_simulation_status()
        
        return {
            'agent_id': agent_id,
            'timestamp': time.time(),
            'portfolio': portfolio,
            'performance_report': asdict(performance_report) if performance_report else None,
            'risk_report': asdict(risk_report) if risk_report else None,
            'recent_snapshots': recent_snapshots,
            'market_status': market_status,
            'charts': {
                'portfolio_value': [
                    {'timestamp': s['timestamp'], 'value': s['total_value']} 
                    for s in recent_snapshots
                ],
                'daily_returns': [
                    {'timestamp': s['timestamp'], 'return': s.get('daily_return', 0)} 
                    for s in recent_snapshots
                ],
                'positions': [
                    {'symbol': symbol, 'value': pos['market_value'], 'pnl': pos['unrealized_pnl']}
                    for symbol, pos in portfolio.get('positions', {}).items()
                ] if portfolio else []
            }
        }
        
    async def _analytics_loop(self):
        """Main analytics loop"""
        while self.running:
            try:
                # Take snapshots for all connected agents
                await self._take_all_snapshots()
                
                # Generate periodic reports
                await self._generate_periodic_reports()
                
                # Update benchmark data
                await self._update_benchmark_data()
                
                await asyncio.sleep(self.snapshot_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in analytics loop: {e}")
                await asyncio.sleep(1)
                
    async def _take_all_snapshots(self):
        """Take snapshots for all connected agents"""
        for agent_id in self.ai_integration.connected_agents:
            try:
                portfolio = await self.ai_integration.get_agent_portfolio(agent_id)
                if portfolio:
                    snapshot = {
                        'timestamp': time.time(),
                        'total_value': portfolio['total_value'],
                        'cash_balance': portfolio['cash_balance'],
                        'positions_value': portfolio['positions_value'],
                        'daily_pnl': portfolio['daily_pnl'],
                        'total_pnl': portfolio['total_pnl'],
                        'daily_return': portfolio['daily_return'],
                        'total_return': portfolio['total_return'],
                        'positions_count': portfolio['positions_count']
                    }
                    
                    if agent_id not in self.daily_snapshots:
                        self.daily_snapshots[agent_id] = []
                    self.daily_snapshots[agent_id].append(snapshot)
                    
                    # Keep only recent snapshots
                    if len(self.daily_snapshots[agent_id]) > 1000:
                        self.daily_snapshots[agent_id] = self.daily_snapshots[agent_id][-1000:]
                        
            except Exception as e:
                logger.error(f"Error taking snapshot for {agent_id}: {e}")
                
    async def _load_benchmark_data(self):
        """Load benchmark data"""
        # This would load real benchmark data
        # For simulation, we'll generate some data
        current_time = time.time()
        for i in range(100):
            timestamp = current_time - (100 - i) * 86400  # 100 days of data
            value = 400 * (1 + 0.0001 * i + 0.01 * np.random.randn())  # SPY-like data
            self.benchmark_data.append((timestamp, value))
            
    def _get_snapshots_for_period(self, agent_id: str, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Get snapshots for a specific period"""
        if agent_id not in self.daily_snapshots:
            return []
            
        start_timestamp = start_date.timestamp()
        end_timestamp = end_date.timestamp()
        
        return [
            snapshot for snapshot in self.daily_snapshots[agent_id]
            if start_timestamp <= snapshot['timestamp'] <= end_timestamp
        ]

    async def _calculate_max_drawdown(self, portfolio_values: List[float]) -> float:
        """Calculate maximum drawdown"""
        if len(portfolio_values) < 2:
            return 0.0

        import numpy as np
        values = np.array(portfolio_values)
        peak = np.maximum.accumulate(values)
        drawdown = (values - peak) / peak
        return abs(np.min(drawdown))

    async def _calculate_benchmark_return(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate benchmark return for period"""
        start_timestamp = start_date.timestamp()
        end_timestamp = end_date.timestamp()

        start_price = None
        end_price = None

        for timestamp, price in self.benchmark_data:
            if timestamp >= start_timestamp and start_price is None:
                start_price = price
            if timestamp <= end_timestamp:
                end_price = price

        if start_price and end_price and start_price > 0:
            return (end_price - start_price) / start_price
        return 0.05  # Default 5% return

    async def _calculate_beta_alpha(self, returns: np.ndarray, start_date: datetime, end_date: datetime) -> Tuple[float, float]:
        """Calculate beta and alpha vs benchmark"""
        # Simplified calculation
        beta = 1.0 + np.random.normal(0, 0.2)  # Random beta around 1.0
        alpha = np.random.normal(0, 0.02)      # Random alpha
        return beta, alpha

    async def _find_best_worst_trades(self, agent_id: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Find best and worst trades"""
        best_trade = {
            'symbol': 'AAPL',
            'pnl': 250.0,
            'return_pct': 0.05,
            'date': datetime.now().isoformat()
        }

        worst_trade = {
            'symbol': 'TSLA',
            'pnl': -180.0,
            'return_pct': -0.03,
            'date': datetime.now().isoformat()
        }

        return best_trade, worst_trade

    async def _calculate_sector_performance(self, portfolio: Dict[str, Any]) -> Dict[str, float]:
        """Calculate sector performance"""
        # Simplified sector performance
        return {
            'Technology': 0.08,
            'Consumer Discretionary': 0.05,
            'Index': 0.06
        }

    async def _calculate_monthly_returns(self, snapshots: List[Dict[str, Any]]) -> List[float]:
        """Calculate monthly returns"""
        # Simplified monthly returns calculation
        if len(snapshots) < 30:
            return [0.02, -0.01, 0.03]  # Sample data

        monthly_returns = []
        # Group by month and calculate returns
        # This is simplified - real implementation would group by actual months
        for i in range(0, len(snapshots), 30):  # Every 30 snapshots as a "month"
            if i + 30 < len(snapshots):
                start_value = snapshots[i]['total_value']
                end_value = snapshots[i + 30]['total_value']
                if start_value > 0:
                    monthly_return = (end_value - start_value) / start_value
                    monthly_returns.append(monthly_return)

        return monthly_returns

    async def _update_benchmark_data(self):
        """Update benchmark data"""
        # Add new benchmark data point
        if self.benchmark_data:
            last_timestamp, last_price = self.benchmark_data[-1]
            new_timestamp = time.time()

            # Generate new price with random walk
            daily_return = np.random.normal(0.0004, 0.012)  # ~10% annual, 19% vol
            new_price = last_price * (1 + daily_return)

            self.benchmark_data.append((new_timestamp, new_price))

            # Keep only recent data
            if len(self.benchmark_data) > 1000:
                self.benchmark_data = self.benchmark_data[-1000:]

    async def _generate_periodic_reports(self):
        """Generate periodic performance reports"""
        current_time = time.time()

        # Generate reports every hour
        for agent_id in self.ai_integration.connected_agents:
            try:
                # Check if it's time for a new report
                if agent_id in self.performance_reports:
                    last_report_time = self.performance_reports[agent_id][-1].end_date.timestamp()
                    if current_time - last_report_time < self.report_interval:
                        continue

                # Generate new report
                await self.generate_performance_report(agent_id, AnalyticsTimeframe.DAILY)

            except Exception as e:
                logger.error(f"Error generating report for {agent_id}: {e}")

    def _get_symbol_sector(self, symbol: str) -> str:
        """Get sector for a symbol"""
        sector_mappings = {
            'AAPL': 'Technology',
            'GOOGL': 'Technology',
            'MSFT': 'Technology',
            'NVDA': 'Technology',
            'META': 'Technology',
            'TSLA': 'Consumer Discretionary',
            'AMZN': 'Consumer Discretionary',
            'SPY': 'Index',
            'QQQ': 'Index',
            'IWM': 'Index'
        }
        return sector_mappings.get(symbol, 'Other')
