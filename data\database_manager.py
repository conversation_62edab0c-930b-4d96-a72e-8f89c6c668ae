"""
Database Manager - Manages all database connections and operations
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
import json
import time

# Optional imports - gracefully handle missing dependencies
try:
    import asyncpg
    HAS_ASYNCPG = True
except ImportError:
    HAS_ASYNCPG = False
    asyncpg = None

try:
    import redis.asyncio as redis
    HAS_REDIS = True
except ImportError:
    HAS_REDIS = False
    redis = None

try:
    from clickhouse_driver import Client as ClickHouseClient
    HAS_CLICKHOUSE = True
except ImportError:
    HAS_CLICKHOUSE = False
    ClickHouseClient = None

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Manages all database connections and operations for the trading system.
    Handles PostgreSQL, Redis, and ClickHouse connections.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_config = config.get('database', {})  # Changed from 'databases' to 'database'

        # Database connections
        self.postgres_pool = None
        self.redis_client = None
        self.clickhouse_client = None

        # Remove in-memory fallback storage - use real databases only
        # self.memory_storage = {
        #     'market_data': {},
        #     'trades': [],
        #     'portfolios': {},
        #     'strategies': {},
        #     'performance': {},
        #     'agents': {},
        #     'teams': {}
        # }

        # State
        self.initialized = False
        self.running = False
        self.connected = False  # Only connected when real databases are available
        
    async def initialize(self):
        """Initialize all database connections"""
        if self.initialized:
            return
            
        logger.info("Initializing Database Manager...")
        
        try:
            # Initialize PostgreSQL
            await self._init_postgres()
            
            # Initialize Redis
            await self._init_redis()
            
            # Initialize ClickHouse
            await self._init_clickhouse()
            
            # Create database schemas
            await self._create_schemas()
            
            self.initialized = True
            logger.info("✓ Database Manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Database Manager: {e}")
            await self.cleanup()
            raise
            
    async def _init_postgres(self):
        """Initialize PostgreSQL connection pool"""
        if not HAS_ASYNCPG:
            raise RuntimeError("asyncpg package is required for PostgreSQL connection")

        postgres_config = self.db_config.get('postgres', {})

        try:
            # Use connection string if provided, otherwise build from components
            connection_string = postgres_config.get('connection_string')
            if connection_string:
                self.postgres_pool = await asyncpg.create_pool(
                    connection_string,
                    min_size=5,
                    max_size=postgres_config.get('pool_size', 20)
                )
            else:
                self.postgres_pool = await asyncpg.create_pool(
                    host=postgres_config.get('host', 'localhost'),
                    port=postgres_config.get('port', 5432),
                    database=postgres_config.get('database', 'trading_agents'),
                    user=postgres_config.get('username', 'trading_user'),
                    password=postgres_config.get('password', 'trading_password'),
                    min_size=5,
                    max_size=postgres_config.get('pool_size', 20)
                )

            # Test connection
            async with self.postgres_pool.acquire() as conn:
                await conn.fetchval('SELECT 1')

            logger.info("✓ PostgreSQL connection pool created and tested")
            self.connected = True

        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL: {e}")
            raise RuntimeError(f"PostgreSQL connection required but failed: {e}")
            
    async def _init_redis(self):
        """Initialize Redis connection"""
        if not HAS_REDIS:
            raise RuntimeError("redis package is required for Redis connection")

        redis_config = self.db_config.get('redis', {})
        if not redis_config.get('enabled', True):
            raise RuntimeError("Redis is disabled in configuration but required for system operation")

        try:
            self.redis_client = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0),  # Changed from 'database' to 'db'
                password=redis_config.get('password'),
                max_connections=redis_config.get('max_connections', 100),
                decode_responses=True
            )

            # Test connection
            await self.redis_client.ping()
            logger.info("✓ Redis connection established and tested")

        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            raise RuntimeError(f"Redis connection required but failed: {e}")
            
    async def _init_clickhouse(self):
        """Initialize ClickHouse connection"""
        if not HAS_CLICKHOUSE:
            raise RuntimeError("clickhouse-driver package is required for ClickHouse connection")

        clickhouse_config = self.db_config.get('clickhouse', {})
        if not clickhouse_config.get('enabled', True):
            raise RuntimeError("ClickHouse is disabled in configuration but required for system operation")

        try:
            self.clickhouse_client = ClickHouseClient(
                host=clickhouse_config.get('host', 'localhost'),
                port=clickhouse_config.get('port', 9000),
                database=clickhouse_config.get('database', 'trading_system'),
                user=clickhouse_config.get('username', 'default'),
                password=clickhouse_config.get('password', '')
            )

            # Test connection
            self.clickhouse_client.execute('SELECT 1')
            logger.info("✓ ClickHouse connection established and tested")

        except Exception as e:
            logger.error(f"Failed to initialize ClickHouse: {e}")
            raise RuntimeError(f"ClickHouse connection required but failed: {e}")
            
    async def _create_schemas(self):
        """Create database schemas and tables"""
        # PostgreSQL schemas
        if self.postgres_pool:
            await self._create_postgres_schemas()
            
        # ClickHouse schemas
        if self.clickhouse_client:
            await self._create_clickhouse_schemas()
            
    async def _create_postgres_schemas(self):
        """Create PostgreSQL schemas"""
        schemas = [
            # Agents table
            """
            CREATE TABLE IF NOT EXISTS agents (
                id UUID PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                role VARCHAR(100) NOT NULL,
                state VARCHAR(50) NOT NULL,
                config JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # Teams table
            """
            CREATE TABLE IF NOT EXISTS teams (
                id UUID PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                type VARCHAR(100) NOT NULL,
                status VARCHAR(50) NOT NULL,
                config JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # Team members table
            """
            CREATE TABLE IF NOT EXISTS team_members (
                team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
                agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
                role VARCHAR(100) NOT NULL,
                joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (team_id, agent_id)
            )
            """,
            
            # Model configurations table
            """
            CREATE TABLE IF NOT EXISTS model_configs (
                id UUID PRIMARY KEY,
                model_name VARCHAR(255) NOT NULL,
                role VARCHAR(100) NOT NULL,
                parameters JSONB NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(model_name, role)
            )
            """,
            
            # Strategies table
            """
            CREATE TABLE IF NOT EXISTS strategies (
                id UUID PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                type VARCHAR(100) NOT NULL,
                parameters JSONB NOT NULL,
                performance_metrics JSONB,
                created_by UUID REFERENCES agents(id),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        ]
        
        async with self.postgres_pool.acquire() as conn:
            for schema in schemas:
                try:
                    await conn.execute(schema)
                except Exception as e:
                    logger.error(f"Error creating schema: {e}")
                    
        logger.info("✓ PostgreSQL schemas created")
        
    async def _create_clickhouse_schemas(self):
        """Create ClickHouse schemas"""
        schemas = [
            # Performance metrics table
            """
            CREATE TABLE IF NOT EXISTS performance_metrics (
                timestamp DateTime,
                agent_id String,
                agent_name String,
                role String,
                metric_type String,
                metric_name String,
                metric_value Float64,
                metadata String
            ) ENGINE = MergeTree()
            ORDER BY (timestamp, agent_id, metric_type)
            """,
            
            # Trading events table
            """
            CREATE TABLE IF NOT EXISTS trading_events (
                timestamp DateTime,
                event_type String,
                agent_id String,
                team_id String,
                symbol String,
                action String,
                quantity Float64,
                price Float64,
                metadata String
            ) ENGINE = MergeTree()
            ORDER BY (timestamp, event_type, symbol)
            """,
            
            # Market data table
            """
            CREATE TABLE IF NOT EXISTS market_data (
                timestamp DateTime,
                symbol String,
                data_type String,
                open Float64,
                high Float64,
                low Float64,
                close Float64,
                volume Float64,
                metadata String
            ) ENGINE = MergeTree()
            ORDER BY (timestamp, symbol, data_type)
            """
        ]
        
        for schema in schemas:
            try:
                self.clickhouse_client.execute(schema)
            except Exception as e:
                logger.error(f"Error creating ClickHouse schema: {e}")
                
        logger.info("✓ ClickHouse schemas created")
        
    async def start(self):
        """Start the database manager"""
        if not self.initialized:
            await self.initialize()
            
        self.running = True
        logger.info("✓ Database Manager started")
        
    async def stop(self):
        """Stop the database manager"""
        if not self.running:
            return
            
        logger.info("Stopping Database Manager...")
        self.running = False
        
        await self.cleanup()
        logger.info("✓ Database Manager stopped")
        
    async def cleanup(self):
        """Cleanup database connections"""
        if self.postgres_pool:
            await self.postgres_pool.close()
            self.postgres_pool = None
            
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
            
        # ClickHouse client doesn't need explicit cleanup
        self.clickhouse_client = None
        
    # PostgreSQL operations
    async def execute_postgres_query(self, query: str, *args) -> List[Dict[str, Any]]:
        """Execute a PostgreSQL query"""
        if not self.postgres_pool:
            logger.warning("PostgreSQL not available")
            return []
            
        try:
            async with self.postgres_pool.acquire() as conn:
                rows = await conn.fetch(query, *args)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"PostgreSQL query error: {e}")
            return []
            
    async def execute_postgres_command(self, command: str, *args) -> bool:
        """Execute a PostgreSQL command"""
        if not self.postgres_pool:
            logger.warning("PostgreSQL not available")
            return False
            
        try:
            async with self.postgres_pool.acquire() as conn:
                await conn.execute(command, *args)
                return True
        except Exception as e:
            logger.error(f"PostgreSQL command error: {e}")
            return False
            
    # Redis operations
    async def redis_set(self, key: str, value: Any, expire: int = None) -> bool:
        """Set a value in Redis"""
        if not self.redis_client:
            logger.warning("Redis not available")
            return False
            
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
                
            await self.redis_client.set(key, value, ex=expire)
            return True
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            return False
            
    async def redis_get(self, key: str) -> Optional[Any]:
        """Get a value from Redis"""
        if not self.redis_client:
            logger.warning("Redis not available")
            return None
            
        try:
            value = await self.redis_client.get(key)
            if value:
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return value
            return None
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
            
    async def redis_publish(self, channel: str, message: Any) -> bool:
        """Publish a message to Redis channel"""
        if not self.redis_client:
            logger.warning("Redis not available")
            return False
            
        try:
            if isinstance(message, (dict, list)):
                message = json.dumps(message)
                
            await self.redis_client.publish(channel, message)
            return True
        except Exception as e:
            logger.error(f"Redis publish error: {e}")
            return False
            
    # ClickHouse operations
    def execute_clickhouse_query(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute a ClickHouse query"""
        if not self.clickhouse_client:
            logger.warning("ClickHouse not available")
            return []
            
        try:
            result = self.clickhouse_client.execute(query, params or {}, with_column_types=True)
            if result:
                columns = [col[0] for col in result[1]]
                return [dict(zip(columns, row)) for row in result[0]]
            return []
        except Exception as e:
            logger.error(f"ClickHouse query error: {e}")
            return []
            
    def insert_clickhouse_data(self, table: str, data: List[Dict[str, Any]]) -> bool:
        """Insert data into ClickHouse table"""
        if not self.clickhouse_client or not data:
            return False
            
        try:
            self.clickhouse_client.execute(f"INSERT INTO {table} VALUES", data)
            return True
        except Exception as e:
            logger.error(f"ClickHouse insert error: {e}")
            return False
            
    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all databases"""
        status = {
            'postgres': 'unavailable',
            'redis': 'unavailable',
            'clickhouse': 'unavailable'
        }
        
        # Check PostgreSQL
        if self.postgres_pool:
            try:
                async with self.postgres_pool.acquire() as conn:
                    await conn.fetchval('SELECT 1')
                status['postgres'] = 'healthy'
            except Exception:
                status['postgres'] = 'unhealthy'
                
        # Check Redis
        if self.redis_client:
            try:
                await self.redis_client.ping()
                status['redis'] = 'healthy'
            except Exception:
                status['redis'] = 'unhealthy'
                
        # Check ClickHouse
        if self.clickhouse_client:
            try:
                self.clickhouse_client.execute('SELECT 1')
                status['clickhouse'] = 'healthy'
            except Exception:
                status['clickhouse'] = 'unhealthy'
                
        return status

    # Real database storage methods - no fallbacks

    async def store_market_data(self, symbol: str, data: List[Dict[str, Any]]) -> bool:
        """Store market data in ClickHouse"""
        if not self.clickhouse_client:
            logger.error("ClickHouse not available for storing market data")
            return False

        try:
            # Prepare data for ClickHouse
            clickhouse_data = []
            for item in data:
                clickhouse_data.append({
                    'timestamp': item.get('timestamp', time.time()),
                    'symbol': symbol,
                    'data_type': item.get('data_type', 'price'),
                    'open': float(item.get('open', 0)),
                    'high': float(item.get('high', 0)),
                    'low': float(item.get('low', 0)),
                    'close': float(item.get('close', 0)),
                    'volume': float(item.get('volume', 0)),
                    'metadata': json.dumps(item.get('metadata', {}))
                })

            return self.insert_clickhouse_data('market_data', clickhouse_data)
        except Exception as e:
            logger.error(f"Error storing market data: {e}")
            return False

    async def get_market_data(self, symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get market data from ClickHouse"""
        if not self.clickhouse_client:
            logger.error("ClickHouse not available for retrieving market data")
            return []

        try:
            query = """
                SELECT * FROM market_data
                WHERE symbol = %(symbol)s
                AND timestamp >= %(start_date)s
                AND timestamp <= %(end_date)s
                ORDER BY timestamp DESC
                LIMIT 1000
            """
            return self.execute_clickhouse_query(query, {
                'symbol': symbol,
                'start_date': start_date,
                'end_date': end_date
            })
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return []

    async def store_trade(self, trade_data: Dict[str, Any]) -> bool:
        """Store trade data in ClickHouse"""
        if not self.clickhouse_client:
            logger.error("ClickHouse not available for storing trade data")
            return False

        try:
            clickhouse_data = [{
                'timestamp': trade_data.get('timestamp', time.time()),
                'event_type': 'trade',
                'agent_id': trade_data.get('agent_id', ''),
                'team_id': trade_data.get('team_id', ''),
                'symbol': trade_data.get('symbol', ''),
                'action': trade_data.get('action', ''),
                'quantity': float(trade_data.get('quantity', 0)),
                'price': float(trade_data.get('price', 0)),
                'metadata': json.dumps(trade_data.get('metadata', {}))
            }]

            return self.insert_clickhouse_data('trading_events', clickhouse_data)
        except Exception as e:
            logger.error(f"Error storing trade: {e}")
            return False

    async def get_trades(self, symbol: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get trades from ClickHouse"""
        if not self.clickhouse_client:
            logger.error("ClickHouse not available for retrieving trades")
            return []

        try:
            query = """
                SELECT * FROM trading_events
                WHERE event_type = 'trade'
            """
            params = {}

            if symbol:
                query += " AND symbol = %(symbol)s"
                params['symbol'] = symbol

            query += " ORDER BY timestamp DESC LIMIT %(limit)s"
            params['limit'] = limit

            return self.execute_clickhouse_query(query, params)
        except Exception as e:
            logger.error(f"Error getting trades: {e}")
            return []
