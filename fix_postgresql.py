#!/usr/bin/env python3
"""
PostgreSQL Fix Script
Diagnoses and fixes PostgreSQL integration issues
"""

import asyncio
import sys
import subprocess
import time
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

async def check_postgresql_service():
    """Check if PostgreSQL service is running"""
    print("🔍 Checking PostgreSQL service status...")
    
    try:
        # Check if PostgreSQL is running on port 5432
        result = subprocess.run(
            ["netstat", "-an"],
            capture_output=True,
            text=True,
            shell=True
        )
        
        if ":5432" in result.stdout:
            print("✅ PostgreSQL service is running on port 5432")
            return True
        else:
            print("❌ PostgreSQL service is NOT running on port 5432")
            return False
            
    except Exception as e:
        print(f"❌ Error checking PostgreSQL service: {e}")
        return False

async def check_postgresql_packages():
    """Check if required PostgreSQL packages are installed"""
    print("🔍 Checking PostgreSQL packages...")
    
    packages = {
        'asyncpg': False,
        'psycopg2': False,
        'psycopg2-binary': False
    }
    
    for package in packages.keys():
        try:
            __import__(package.replace('-', '_'))
            packages[package] = True
            print(f"✅ {package} is installed")
        except ImportError:
            print(f"❌ {package} is NOT installed")
    
    return any(packages.values())

async def install_postgresql_packages():
    """Install required PostgreSQL packages"""
    print("📦 Installing PostgreSQL packages...")
    
    packages_to_install = ['asyncpg', 'psycopg2-binary']
    
    for package in packages_to_install:
        try:
            print(f"Installing {package}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✅ {package} installed successfully")
            else:
                print(f"❌ Failed to install {package}: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error installing {package}: {e}")

async def test_postgresql_connection():
    """Test PostgreSQL connection"""
    print("🧪 Testing PostgreSQL connection...")
    
    try:
        import asyncpg
        
        # Connection parameters from config
        conn = await asyncpg.connect(
            host="127.0.0.1",
            port=5432,
            database="postgres",
            user="postgres",
            password="Zaman2009"
        )
        
        # Test query
        result = await conn.fetchval('SELECT 1')
        await conn.close()
        
        if result == 1:
            print("✅ PostgreSQL connection successful")
            return True
        else:
            print("❌ PostgreSQL connection failed - unexpected result")
            return False
            
    except ImportError:
        print("❌ asyncpg not available")
        return False
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False

async def start_postgresql_service():
    """Attempt to start PostgreSQL service"""
    print("🚀 Attempting to start PostgreSQL service...")
    
    try:
        # Try different methods to start PostgreSQL
        commands = [
            ["net", "start", "postgresql"],
            ["sc", "start", "postgresql"],
            ["pg_ctl", "start", "-D", "C:\\Program Files\\PostgreSQL\\15\\data"],
            ["pg_ctl", "start", "-D", "C:\\Program Files\\PostgreSQL\\14\\data"],
            ["pg_ctl", "start", "-D", "C:\\Program Files\\PostgreSQL\\13\\data"]
        ]
        
        for cmd in commands:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    print(f"✅ PostgreSQL started with command: {' '.join(cmd)}")
                    return True
                else:
                    print(f"⚠️ Command failed: {' '.join(cmd)} - {result.stderr}")
            except Exception as e:
                print(f"⚠️ Command error: {' '.join(cmd)} - {e}")
        
        print("❌ Could not start PostgreSQL service")
        return False
        
    except Exception as e:
        print(f"❌ Error starting PostgreSQL: {e}")
        return False

async def create_database_if_needed():
    """Create trading database if it doesn't exist"""
    print("🗄️ Checking/creating trading database...")
    
    try:
        import asyncpg
        
        # Connect to default postgres database
        conn = await asyncpg.connect(
            host="127.0.0.1",
            port=5432,
            database="postgres",
            user="postgres",
            password="Zaman2009"
        )
        
        # Check if trading database exists
        result = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = 'trading_system'"
        )
        
        if not result:
            # Create trading database
            await conn.execute("CREATE DATABASE trading_system")
            print("✅ Created trading_system database")
        else:
            print("✅ trading_system database already exists")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        return False

async def test_database_manager():
    """Test the database manager"""
    print("🧪 Testing Database Manager...")
    
    try:
        from data.database_manager import DatabaseManager
        import yaml
        
        # Load config
        config_path = Path("config/system_config.yaml")
        if not config_path.exists():
            print("❌ Config file not found")
            return False
            
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Test database manager
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        health_status = await db_manager.get_health_status()
        
        print(f"📊 Database health status: {health_status}")
        
        if health_status.get('postgres') == 'healthy':
            print("✅ Database Manager PostgreSQL connection successful")
            await db_manager.cleanup()
            return True
        else:
            print(f"❌ Database Manager PostgreSQL status: {health_status.get('postgres')}")
            await db_manager.cleanup()
            return False
            
    except Exception as e:
        print(f"❌ Database Manager test failed: {e}")
        return False

async def main():
    """Main fix function"""
    print("🔧 PostgreSQL Fix Script")
    print("=" * 50)
    
    # Step 1: Check if PostgreSQL service is running
    service_running = await check_postgresql_service()
    
    # Step 2: Check if packages are installed
    packages_available = await check_postgresql_packages()
    
    # Step 3: Install packages if needed
    if not packages_available:
        await install_postgresql_packages()
        packages_available = await check_postgresql_packages()
    
    # Step 4: Start PostgreSQL if not running
    if not service_running:
        service_started = await start_postgresql_service()
        if service_started:
            # Wait a moment for service to fully start
            print("⏳ Waiting for PostgreSQL to fully start...")
            await asyncio.sleep(5)
            service_running = await check_postgresql_service()
    
    # Step 5: Test connection
    if service_running and packages_available:
        connection_ok = await test_postgresql_connection()
        
        if connection_ok:
            # Step 6: Create database if needed
            await create_database_if_needed()
            
            # Step 7: Test database manager
            await test_database_manager()
    
    # Summary
    print("\n📊 SUMMARY:")
    print("=" * 50)
    print(f"PostgreSQL Service: {'✅ RUNNING' if service_running else '❌ NOT RUNNING'}")
    print(f"Required Packages: {'✅ INSTALLED' if packages_available else '❌ MISSING'}")
    
    if service_running and packages_available:
        print("🎉 PostgreSQL is ready for use!")
        return True
    else:
        print("⚠️ PostgreSQL needs manual setup")
        print("\nMANUAL STEPS NEEDED:")
        if not service_running:
            print("1. Install PostgreSQL from https://www.postgresql.org/download/")
            print("2. Start PostgreSQL service")
        if not packages_available:
            print("3. Install Python packages: pip install asyncpg psycopg2-binary")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Fix interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fix failed with error: {e}")
        sys.exit(1)
