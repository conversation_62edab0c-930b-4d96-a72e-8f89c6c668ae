{"timestamp": "2025-06-20T20:32:33.104696", "test_type": "production_deployment_preparation", "initialization_success": true, "preparation_report": {"overall_status": "failed", "total_checks": 10, "passed_checks": 6, "failed_checks": 1, "warning_checks": 3, "critical_failures": 1, "deployment_ready": false, "recommendations": ["Fix critical failures before deployment", "Resolve all failed checks", "Review and address warnings"]}, "validation_report": {"overall_status": "failed", "total_checks": 14, "passed_checks": 10, "failed_checks": 1, "warning_checks": 3, "critical_failures": 1, "deployment_ready": false, "recommendations": ["Fix critical failures before deployment", "Resolve all failed checks", "Review and address warnings"]}, "test_report": {"overall_status": "passed", "total_checks": 4, "passed_checks": 4, "failed_checks": 0, "warning_checks": 0}, "final_status": {"initialized": true, "current_stage": "testing", "deployment_ready": false, "last_validation_time": **********.101694, "stage_history": [["preparation", **********.080683], ["preparation", **********.080683], ["validation", **********.0873516], ["testing", **********.1026974]], "total_checks_run": 4, "critical_requirements": {"system_memory": 8, "system_cpu_cores": 4, "disk_space": 50, "python_version": "3.8", "required_services": ["redis", "postgresql"], "required_models": ["exaone-deep:32b", "magistral-abliterated:24b"]}, "deployment_config": {"environment": "production", "deployment_method": "rolling", "health_check_timeout": 300, "rollback_timeout": 600, "monitoring_enabled": true, "backup_before_deployment": true}}, "checklist_created": true}