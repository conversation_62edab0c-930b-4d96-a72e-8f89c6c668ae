#!/usr/bin/env python3
"""
Quick AI Integration Test
"""

import asyncio
import logging
from system.system_coordinator import SystemCoordinator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    try:
        print("🚀 Quick AI Integration Test")
        print("=" * 50)
        
        # Initialize system coordinator
        coordinator = SystemCoordinator('config/system_config.yaml')
        
        print("📋 Initializing system...")
        init_success = await coordinator.initialize()
        
        if not init_success:
            print("❌ System initialization failed")
            return False
            
        print("✅ System initialized successfully")
        
        # Check AI integration status
        print("\n🤖 Checking AI integration...")
        ai_status = await coordinator.get_ai_integration_status()
        
        print(f"AI Integration Active: {ai_status.get('ai_integration_active', False)}")
        print(f"Available Models: {ai_status.get('available_models', 0)}")
        print(f"Deployed Models: {ai_status.get('deployed_models', 0)}")
        print(f"Active Agents: {ai_status.get('active_agents', 0)}")
        print(f"Formed Teams: {ai_status.get('formed_teams', 0)}")
        
        # Get system health
        print("\n🏥 Checking system health...")
        health = await coordinator.get_comprehensive_health_report()
        
        print(f"System Health: {health.get('overall_health', 0):.1%}")
        print(f"Health Status: {health.get('health_status', 'unknown')}")
        
        # Check component health
        component_health = health.get('component_health', {})
        healthy_components = sum(1 for comp in component_health.values() if comp.get('running', False))
        total_components = len(component_health)
        
        print(f"Component Health: {healthy_components}/{total_components} healthy")
        
        # Test AI activation if not already active
        if not ai_status.get('ai_integration_active', False):
            print("\n🔄 Activating AI integration...")
            activation_result = await coordinator.activate_ai_integration()
            
            if activation_result.get('success', False):
                print("✅ AI integration activated successfully")
                
                # Get updated status
                updated_ai_status = await coordinator.get_ai_integration_status()
                print(f"Updated - Active Agents: {updated_ai_status.get('active_agents', 0)}")
                print(f"Updated - Formed Teams: {updated_ai_status.get('formed_teams', 0)}")
            else:
                print("❌ AI integration activation failed")
                print(f"Error: {activation_result.get('error', 'Unknown error')}")
        
        # Final summary
        print("\n📊 FINAL RESULTS:")
        print("=" * 50)
        
        final_health = await coordinator.get_comprehensive_health_report()
        final_ai_status = await coordinator.get_ai_integration_status()
        
        overall_health = final_health.get('overall_health', 0)
        ai_active = final_ai_status.get('ai_integration_active', False)
        
        if overall_health >= 0.8 and ai_active:
            status = "🟢 EXCELLENT"
        elif overall_health >= 0.6:
            status = "🟡 GOOD"
        elif overall_health >= 0.4:
            status = "🟠 FAIR"
        else:
            status = "🔴 POOR"
            
        print(f"Overall Status: {status}")
        print(f"System Health: {overall_health:.1%}")
        print(f"AI Integration: {'✅ ACTIVE' if ai_active else '❌ INACTIVE'}")
        print(f"Available Models: {final_ai_status.get('available_models', 0)}")
        print(f"Deployed Models: {final_ai_status.get('deployed_models', 0)}")
        print(f"Active Agents: {final_ai_status.get('active_agents', 0)}")
        print(f"Formed Teams: {final_ai_status.get('formed_teams', 0)}")
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        await coordinator.stop()
        print("✅ Test completed successfully")
        
        return overall_health >= 0.6 and ai_active
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
