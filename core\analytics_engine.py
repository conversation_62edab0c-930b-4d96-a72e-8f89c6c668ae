"""
Analytics Engine - Advanced analytics, performance tracking, and insights generation
"""

import asyncio
import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
from collections import deque, defaultdict
import json
import statistics

logger = logging.getLogger(__name__)


class AnalyticsType(Enum):
    """Types of analytics"""
    PERFORMANCE = "performance"
    RISK = "risk"
    MARKET = "market"
    STRATEGY = "strategy"
    PORTFOLIO = "portfolio"
    BEHAVIORAL = "behavioral"


@dataclass
class PerformanceMetrics:
    """Performance metrics data"""
    timestamp: float
    total_return: float
    daily_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float


@dataclass
class RiskMetrics:
    """Risk metrics data"""
    timestamp: float
    var_95: float  # Value at Risk 95%
    var_99: float  # Value at Risk 99%
    expected_shortfall: float
    beta: float
    correlation_spy: float
    volatility: float
    max_position_concentration: float


@dataclass
class MarketAnalytics:
    """Market analytics data"""
    timestamp: float
    market_trend: str
    volatility_regime: str
    correlation_matrix: Dict[str, Dict[str, float]]
    sector_performance: Dict[str, float]
    momentum_indicators: Dict[str, float]


class AnalyticsEngine:
    """
    Advanced analytics engine that provides comprehensive performance tracking,
    risk analysis, market insights, and strategy evaluation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.analytics_config = config.get('analytics', {})
        
        # Data storage
        self.performance_history: deque = deque(maxlen=10000)
        self.risk_history: deque = deque(maxlen=10000)
        self.market_history: deque = deque(maxlen=10000)
        self.trade_history: List[Dict[str, Any]] = []
        self.portfolio_history: deque = deque(maxlen=10000)
        
        # Real-time metrics
        self.current_metrics: Dict[str, Any] = {}
        self.alerts: List[Dict[str, Any]] = []
        
        # Analytics parameters
        self.lookback_period = self.analytics_config.get('lookback_period', 252)  # 1 year
        self.risk_free_rate = self.analytics_config.get('risk_free_rate', 0.02)  # 2%
        self.benchmark_symbol = self.analytics_config.get('benchmark', 'SPY')
        
        # Market data simulation
        self.market_data: Dict[str, List[float]] = defaultdict(list)
        self.benchmark_data: List[float] = []
        
        # Performance tracking
        self.portfolio_values: List[float] = []
        self.daily_returns: List[float] = []
        self.trade_analytics: Dict[str, Any] = {}
        
        # State
        self.initialized = False
        self.analytics_active = True
        
    async def initialize(self) -> bool:
        """Initialize the analytics engine"""
        try:
            logger.info("🚀 Initializing Analytics Engine...")
            
            # Initialize data structures
            await self._initialize_data_structures()
            
            # Setup analytics calculations
            await self._setup_analytics_calculations()
            
            # Initialize market data simulation
            await self._initialize_market_simulation()
            
            # Start analytics processing
            await self._start_analytics_processing()
            
            self.initialized = True
            logger.info("✅ Analytics Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Analytics Engine: {e}")
            return False
            
    async def update_portfolio_data(self, portfolio_value: float, positions: Dict[str, Any], 
                                   trades: List[Dict[str, Any]]) -> bool:
        """Update portfolio data for analytics"""
        try:
            timestamp = time.time()
            
            # Update portfolio value history
            self.portfolio_values.append(portfolio_value)
            
            # Calculate daily return
            if len(self.portfolio_values) > 1:
                daily_return = (portfolio_value - self.portfolio_values[-2]) / self.portfolio_values[-2]
                self.daily_returns.append(daily_return)
            else:
                self.daily_returns.append(0.0)
                
            # Store portfolio snapshot
            portfolio_snapshot = {
                'timestamp': timestamp,
                'portfolio_value': portfolio_value,
                'positions': positions,
                'daily_return': self.daily_returns[-1] if self.daily_returns else 0.0
            }
            self.portfolio_history.append(portfolio_snapshot)
            
            # Update trade history
            for trade in trades:
                if trade not in self.trade_history:
                    self.trade_history.append(trade)
                    
            # Trigger analytics update
            await self._update_analytics()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to update portfolio data: {e}")
            return False
            
    async def calculate_performance_metrics(self) -> Optional[PerformanceMetrics]:
        """Calculate comprehensive performance metrics"""
        try:
            if len(self.portfolio_values) < 2:
                return None
                
            # Calculate returns
            returns = np.array(self.daily_returns)
            total_return = (self.portfolio_values[-1] - self.portfolio_values[0]) / self.portfolio_values[0]
            
            # Calculate volatility (annualized)
            volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0.0
            
            # Calculate Sharpe ratio
            excess_returns = returns - (self.risk_free_rate / 252)
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0.0
            
            # Calculate maximum drawdown
            portfolio_values = np.array(self.portfolio_values)
            peak = np.maximum.accumulate(portfolio_values)
            drawdown = (portfolio_values - peak) / peak
            max_drawdown = np.min(drawdown)
            
            # Calculate trade-based metrics
            profitable_trades = len([t for t in self.trade_history if t.get('pnl', 0) > 0])
            total_trades = len(self.trade_history)
            win_rate = profitable_trades / max(1, total_trades)
            
            # Calculate profit factor
            gross_profit = sum([t.get('pnl', 0) for t in self.trade_history if t.get('pnl', 0) > 0])
            gross_loss = abs(sum([t.get('pnl', 0) for t in self.trade_history if t.get('pnl', 0) < 0]))
            profit_factor = gross_profit / max(1, gross_loss)
            
            # Calculate average trade duration
            trade_durations = [t.get('duration', 0) for t in self.trade_history if 'duration' in t]
            avg_trade_duration = statistics.mean(trade_durations) if trade_durations else 0.0
            
            metrics = PerformanceMetrics(
                timestamp=time.time(),
                total_return=total_return,
                daily_return=returns[-1] if len(returns) > 0 else 0.0,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                total_trades=total_trades,
                avg_trade_duration=avg_trade_duration
            )
            
            self.performance_history.append(metrics)
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate performance metrics: {e}")
            return None
            
    async def calculate_risk_metrics(self, positions: Dict[str, Any]) -> Optional[RiskMetrics]:
        """Calculate comprehensive risk metrics"""
        try:
            if len(self.daily_returns) < 30:  # Need at least 30 days
                return None
                
            returns = np.array(self.daily_returns[-252:])  # Last year
            
            # Calculate Value at Risk
            var_95 = np.percentile(returns, 5)
            var_99 = np.percentile(returns, 1)
            
            # Calculate Expected Shortfall (Conditional VaR)
            expected_shortfall = np.mean(returns[returns <= var_95])
            
            # Calculate beta (vs benchmark)
            if len(self.benchmark_data) >= len(returns):
                benchmark_returns = np.array(self.benchmark_data[-len(returns):])
                covariance = np.cov(returns, benchmark_returns)[0, 1]
                benchmark_variance = np.var(benchmark_returns)
                beta = covariance / benchmark_variance if benchmark_variance > 0 else 1.0
                correlation_spy = np.corrcoef(returns, benchmark_returns)[0, 1]
            else:
                beta = 1.0
                correlation_spy = 0.0
                
            # Calculate volatility
            volatility = np.std(returns) * np.sqrt(252)
            
            # Calculate position concentration
            total_value = sum([pos.get('market_value', 0) for pos in positions.values()])
            max_position_value = max([pos.get('market_value', 0) for pos in positions.values()]) if positions else 0
            max_position_concentration = max_position_value / max(1, total_value)
            
            metrics = RiskMetrics(
                timestamp=time.time(),
                var_95=var_95,
                var_99=var_99,
                expected_shortfall=expected_shortfall,
                beta=beta,
                correlation_spy=correlation_spy,
                volatility=volatility,
                max_position_concentration=max_position_concentration
            )
            
            self.risk_history.append(metrics)
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate risk metrics: {e}")
            return None
            
    async def analyze_market_conditions(self) -> Optional[MarketAnalytics]:
        """Analyze current market conditions"""
        try:
            # Simulate market analysis
            market_trend = "bullish" if np.random.random() > 0.5 else "bearish"
            volatility_regime = "low" if np.random.random() > 0.3 else "high"
            
            # Generate correlation matrix
            symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN']
            correlation_matrix = {}
            for symbol1 in symbols:
                correlation_matrix[symbol1] = {}
                for symbol2 in symbols:
                    if symbol1 == symbol2:
                        correlation_matrix[symbol1][symbol2] = 1.0
                    else:
                        correlation_matrix[symbol1][symbol2] = np.random.uniform(0.3, 0.8)
                        
            # Generate sector performance
            sectors = ['Technology', 'Healthcare', 'Finance', 'Energy', 'Consumer']
            sector_performance = {sector: np.random.uniform(-0.05, 0.05) for sector in sectors}
            
            # Generate momentum indicators
            momentum_indicators = {
                'rsi': np.random.uniform(30, 70),
                'macd': np.random.uniform(-1, 1),
                'momentum': np.random.uniform(-0.1, 0.1)
            }
            
            analytics = MarketAnalytics(
                timestamp=time.time(),
                market_trend=market_trend,
                volatility_regime=volatility_regime,
                correlation_matrix=correlation_matrix,
                sector_performance=sector_performance,
                momentum_indicators=momentum_indicators
            )
            
            self.market_history.append(analytics)
            return analytics
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze market conditions: {e}")
            return None
            
    async def generate_insights(self) -> List[Dict[str, Any]]:
        """Generate actionable insights from analytics"""
        try:
            insights = []
            
            # Performance insights
            if self.performance_history:
                latest_perf = self.performance_history[-1]
                
                if latest_perf.sharpe_ratio > 1.5:
                    insights.append({
                        'type': 'performance',
                        'level': 'positive',
                        'message': f'Excellent risk-adjusted returns with Sharpe ratio of {latest_perf.sharpe_ratio:.2f}',
                        'metric': 'sharpe_ratio',
                        'value': latest_perf.sharpe_ratio
                    })
                elif latest_perf.sharpe_ratio < 0.5:
                    insights.append({
                        'type': 'performance',
                        'level': 'warning',
                        'message': f'Low risk-adjusted returns with Sharpe ratio of {latest_perf.sharpe_ratio:.2f}',
                        'metric': 'sharpe_ratio',
                        'value': latest_perf.sharpe_ratio
                    })
                    
                if latest_perf.max_drawdown < -0.15:
                    insights.append({
                        'type': 'risk',
                        'level': 'warning',
                        'message': f'High maximum drawdown of {latest_perf.max_drawdown:.1%}',
                        'metric': 'max_drawdown',
                        'value': latest_perf.max_drawdown
                    })
                    
            # Risk insights
            if self.risk_history:
                latest_risk = self.risk_history[-1]
                
                if latest_risk.max_position_concentration > 0.3:
                    insights.append({
                        'type': 'risk',
                        'level': 'warning',
                        'message': f'High position concentration: {latest_risk.max_position_concentration:.1%} in single position',
                        'metric': 'concentration',
                        'value': latest_risk.max_position_concentration
                    })
                    
                if latest_risk.volatility > 0.25:
                    insights.append({
                        'type': 'risk',
                        'level': 'warning',
                        'message': f'High portfolio volatility: {latest_risk.volatility:.1%} annualized',
                        'metric': 'volatility',
                        'value': latest_risk.volatility
                    })
                    
            # Market insights
            if self.market_history:
                latest_market = self.market_history[-1]
                
                insights.append({
                    'type': 'market',
                    'level': 'info',
                    'message': f'Current market trend: {latest_market.market_trend}, volatility regime: {latest_market.volatility_regime}',
                    'metric': 'market_conditions',
                    'value': {'trend': latest_market.market_trend, 'volatility': latest_market.volatility_regime}
                })
                
            return insights
            
        except Exception as e:
            logger.error(f"❌ Failed to generate insights: {e}")
            return []
            
    async def get_analytics_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive analytics dashboard data"""
        try:
            dashboard = {
                'timestamp': time.time(),
                'performance_metrics': asdict(self.performance_history[-1]) if self.performance_history else None,
                'risk_metrics': asdict(self.risk_history[-1]) if self.risk_history else None,
                'market_analytics': asdict(self.market_history[-1]) if self.market_history else None,
                'insights': await self.generate_insights(),
                'portfolio_summary': {
                    'total_trades': len(self.trade_history),
                    'portfolio_value_history': self.portfolio_values[-30:] if len(self.portfolio_values) >= 30 else self.portfolio_values,
                    'daily_returns_history': self.daily_returns[-30:] if len(self.daily_returns) >= 30 else self.daily_returns,
                    'current_positions': len([p for p in self.portfolio_history[-1]['positions'].values() if p.get('quantity', 0) > 0]) if self.portfolio_history else 0
                },
                'alerts': self.alerts[-10:],  # Last 10 alerts
                'analytics_status': {
                    'initialized': self.initialized,
                    'analytics_active': self.analytics_active,
                    'data_points': len(self.portfolio_history),
                    'lookback_period': self.lookback_period
                }
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"❌ Failed to get analytics dashboard: {e}")
            return {'error': str(e)}
            
    async def _initialize_data_structures(self):
        """Initialize data structures"""
        # Initialize with some historical data simulation
        for i in range(30):  # 30 days of history
            self.portfolio_values.append(1000000 * (1 + np.random.uniform(-0.02, 0.02)))
            self.benchmark_data.append(450 * (1 + np.random.uniform(-0.015, 0.015)))
            
        # Calculate initial daily returns
        for i in range(1, len(self.portfolio_values)):
            daily_return = (self.portfolio_values[i] - self.portfolio_values[i-1]) / self.portfolio_values[i-1]
            self.daily_returns.append(daily_return)
            
    async def _setup_analytics_calculations(self):
        """Setup analytics calculation parameters"""
        # Analytics parameters are already configured in __init__
        pass
        
    async def _initialize_market_simulation(self):
        """Initialize market data simulation"""
        # Generate some historical market data
        symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN']
        for symbol in symbols:
            for i in range(252):  # 1 year of data
                if i == 0:
                    self.market_data[symbol].append(100.0)
                else:
                    change = np.random.uniform(-0.03, 0.03)
                    new_price = self.market_data[symbol][-1] * (1 + change)
                    self.market_data[symbol].append(new_price)
                    
    async def _start_analytics_processing(self):
        """Start background analytics processing"""
        asyncio.create_task(self._analytics_processing_loop())
        
    async def _analytics_processing_loop(self):
        """Background analytics processing loop"""
        try:
            while self.analytics_active:
                await asyncio.sleep(60)  # Update every minute
                
                # Update market analytics
                await self.analyze_market_conditions()
                
                # Check for alerts
                await self._check_alerts()
                
        except Exception as e:
            logger.error(f"Analytics processing error: {e}")
            
    async def _update_analytics(self):
        """Update all analytics"""
        try:
            # This is called when portfolio data is updated
            # Analytics are calculated on-demand in the respective methods
            pass
            
        except Exception as e:
            logger.error(f"Analytics update error: {e}")
            
    async def _check_alerts(self):
        """Check for alert conditions"""
        try:
            # Check performance alerts
            if self.performance_history:
                latest_perf = self.performance_history[-1]
                
                if latest_perf.max_drawdown < -0.1:
                    self.alerts.append({
                        'timestamp': time.time(),
                        'type': 'risk',
                        'level': 'warning',
                        'message': f'Maximum drawdown exceeded 10%: {latest_perf.max_drawdown:.1%}',
                        'metric': 'max_drawdown',
                        'value': latest_perf.max_drawdown
                    })
                    
            # Limit alerts to prevent memory issues
            if len(self.alerts) > 100:
                self.alerts = self.alerts[-50:]
                
        except Exception as e:
            logger.error(f"Alert checking error: {e}")
            
    async def get_analytics_status(self) -> Dict[str, Any]:
        """Get current analytics engine status"""
        try:
            return {
                'initialized': self.initialized,
                'analytics_active': self.analytics_active,
                'performance_data_points': len(self.performance_history),
                'risk_data_points': len(self.risk_history),
                'market_data_points': len(self.market_history),
                'portfolio_data_points': len(self.portfolio_history),
                'trade_count': len(self.trade_history),
                'alert_count': len(self.alerts),
                'lookback_period': self.lookback_period,
                'benchmark_symbol': self.benchmark_symbol
            }
            
        except Exception as e:
            logger.error(f"Error getting analytics status: {e}")
            return {'error': str(e)}
            
    async def stop(self):
        """Stop the analytics engine"""
        try:
            self.analytics_active = False
            logger.info("✅ Analytics Engine stopped")
            
        except Exception as e:
            logger.error(f"Error stopping analytics engine: {e}")
