"""
Market Simulation Environment - Complete Realistic Market Simulation
Provides comprehensive market environment with multiple assets, regimes, and events
"""

import asyncio
import logging
import time
import math
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime, timedelta

from .mock_market_data import MockMarketDataProvider, MarketRegime, NewsType, NewsEvent
from .trading_engine import SimulatedTradingEngine
from .portfolio_manager import PortfolioManager
from .risk_management import RiskManager
from .backtesting_engine import BacktestingEngine

logger = logging.getLogger(__name__)


class SimulationMode(Enum):
    """Simulation modes"""
    REAL_TIME = "real_time"
    ACCELERATED = "accelerated"
    HISTORICAL = "historical"


class EconomicEvent(Enum):
    """Economic events that affect markets"""
    INTEREST_RATE_CHANGE = "interest_rate_change"
    INFLATION_DATA = "inflation_data"
    EMPLOYMENT_DATA = "employment_data"
    GDP_RELEASE = "gdp_release"
    EARNINGS_SEASON = "earnings_season"
    MARKET_CRASH = "market_crash"
    BULL_RUN = "bull_run"
    RECESSION = "recession"


@dataclass
class SimulationConfig:
    """Simulation configuration"""
    mode: SimulationMode
    duration_days: int
    initial_cash_per_agent: float
    symbols: List[str]
    market_hours_only: bool = True
    enable_news_events: bool = True
    enable_economic_events: bool = True
    volatility_multiplier: float = 1.0
    correlation_strength: float = 1.0
    commission_rate: float = 0.001
    slippage_factor: float = 0.0005
    time_acceleration: float = 1.0  # 1.0 = real time, 10.0 = 10x faster


@dataclass
class SimulationState:
    """Current simulation state"""
    simulation_id: str
    start_time: float
    current_time: float
    elapsed_days: float
    market_regime: MarketRegime
    economic_cycle: str
    volatility_index: float
    market_sentiment: float
    active_agents: int
    total_trades: int
    total_volume: float
    market_cap: float


class MarketSimulator:
    """
    Complete Market Simulation Environment
    
    Features:
    - Multiple market regimes (bull, bear, sideways, volatile)
    - Economic events and cycles
    - News event simulation
    - Multi-asset correlation
    - Realistic volatility clustering
    - Market hours simulation
    - Economic indicators
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Simulation configuration
        self.sim_config = SimulationConfig(
            mode=SimulationMode(config.get('simulation', {}).get('mode', 'real_time')),
            duration_days=config.get('simulation', {}).get('duration_days', 30),
            initial_cash_per_agent=config.get('simulation', {}).get('initial_cash', 100000),
            symbols=config.get('simulation', {}).get('symbols', ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN']),
            market_hours_only=config.get('simulation', {}).get('market_hours_only', True),
            enable_news_events=config.get('simulation', {}).get('enable_news_events', True),
            enable_economic_events=config.get('simulation', {}).get('enable_economic_events', True),
            volatility_multiplier=config.get('simulation', {}).get('volatility_multiplier', 1.0),
            correlation_strength=config.get('simulation', {}).get('correlation_strength', 1.0),
            commission_rate=config.get('simulation', {}).get('commission_rate', 0.001),
            slippage_factor=config.get('simulation', {}).get('slippage_factor', 0.0005),
            time_acceleration=config.get('simulation', {}).get('time_acceleration', 1.0)
        )
        
        # Core components
        self.market_data_provider = MockMarketDataProvider(config)
        self.trading_engine = SimulatedTradingEngine(config, self.market_data_provider)
        self.portfolio_manager = PortfolioManager(config, self.trading_engine, self.market_data_provider)
        self.risk_manager = RiskManager(config)
        self.backtesting_engine = BacktestingEngine(config)
        
        # Simulation state
        self.simulation_state: Optional[SimulationState] = None
        self.registered_agents: Dict[str, Dict[str, Any]] = {}
        self.economic_events: List[Tuple[float, EconomicEvent, Dict[str, Any]]] = []
        
        # Market cycles
        self.economic_cycle_position = 0.0  # 0.0 to 1.0 (full cycle)
        self.cycle_length_days = 365 * 4  # 4-year cycle
        
        # State
        self.initialized = False
        self.running = False
        self.simulation_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize market simulation"""
        try:
            logger.info("Initializing Market Simulation Environment...")
            
            # Initialize all components
            await self.market_data_provider.initialize()
            await self.trading_engine.initialize()
            await self.portfolio_manager.initialize()
            await self.risk_manager.initialize()
            await self.backtesting_engine.initialize()
            
            # Generate economic events schedule
            await self._generate_economic_events()
            
            self.initialized = True
            logger.info("✓ Market Simulation Environment initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Market Simulation: {e}")
            raise
            
    async def start_simulation(self, simulation_id: str = None) -> str:
        """Start market simulation"""
        if not self.initialized:
            await self.initialize()
            
        if not simulation_id:
            simulation_id = f"sim_{int(time.time())}"
            
        # Create simulation state
        self.simulation_state = SimulationState(
            simulation_id=simulation_id,
            start_time=time.time(),
            current_time=time.time(),
            elapsed_days=0.0,
            market_regime=MarketRegime.BULL_MARKET,
            economic_cycle="expansion",
            volatility_index=20.0,
            market_sentiment=0.1,
            active_agents=len(self.registered_agents),
            total_trades=0,
            total_volume=0.0,
            market_cap=0.0
        )
        
        # Start all components
        await self.market_data_provider.start_streaming()
        await self.trading_engine.start()
        await self.portfolio_manager.start_monitoring()
        await self.risk_manager.start()
        
        # Start simulation loop
        self.running = True
        self.simulation_task = asyncio.create_task(self._simulation_loop())
        
        logger.info(f"✓ Market simulation {simulation_id} started")
        return simulation_id
        
    async def stop_simulation(self):
        """Stop market simulation"""
        self.running = False
        
        if self.simulation_task:
            self.simulation_task.cancel()
            try:
                await self.simulation_task
            except asyncio.CancelledError:
                pass
                
        # Stop all components
        await self.market_data_provider.stop_streaming()
        await self.trading_engine.stop()
        await self.portfolio_manager.stop_monitoring()
        await self.risk_manager.stop()
        
        logger.info("✓ Market simulation stopped")
        
    async def register_agent(self, agent_id: str, agent_config: Dict[str, Any] = None) -> bool:
        """Register an AI agent for trading"""
        try:
            # Create trading account
            account = await self.trading_engine.create_account(
                agent_id, 
                self.sim_config.initial_cash_per_agent
            )
            
            # Register with risk manager
            await self.risk_manager.add_risk_limit(
                agent_id,
                self.risk_manager.risk_limits.get('default', [])
            )
            
            # Store agent configuration
            self.registered_agents[agent_id] = {
                'config': agent_config or {},
                'registered_at': time.time(),
                'account': account,
                'active': True
            }
            
            logger.info(f"✓ Agent {agent_id} registered for trading simulation")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register agent {agent_id}: {e}")
            return False
            
    async def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current market data for a symbol"""
        market_data = await self.market_data_provider.get_current_data(symbol)
        if market_data:
            return {
                'symbol': market_data.symbol,
                'price': market_data.close,
                'bid': market_data.bid,
                'ask': market_data.ask,
                'volume': market_data.volume,
                'change': market_data.change,
                'change_percent': market_data.change_percent,
                'timestamp': market_data.timestamp
            }
        return None
        
    async def place_order(self, agent_id: str, symbol: str, side: str, 
                         quantity: float, order_type: str = "market", 
                         price: Optional[float] = None) -> str:
        """Place a trading order for an agent"""
        from .trading_engine import OrderSide, OrderType
        
        # Convert string parameters to enums
        order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
        order_type_enum = OrderType.MARKET if order_type.lower() == 'market' else OrderType.LIMIT
        
        # Check if agent is registered
        if agent_id not in self.registered_agents:
            raise ValueError(f"Agent {agent_id} not registered")
            
        # Check risk limits
        account = await self.trading_engine.get_account(agent_id)
        risk_check = await self.risk_manager.check_position_risk(
            agent_id, symbol, quantity, account.total_value
        )
        
        if not risk_check['approved']:
            raise ValueError(f"Order rejected by risk management: {risk_check['violations']}")
            
        # Place order
        order_id = await self.trading_engine.place_order(
            agent_id, symbol, order_side, quantity, order_type_enum, price
        )
        
        # Update simulation statistics
        if self.simulation_state:
            self.simulation_state.total_trades += 1
            
        return order_id
        
    async def get_portfolio(self, agent_id: str) -> Dict[str, Any]:
        """Get agent's portfolio information"""
        return await self.portfolio_manager.get_portfolio_summary(agent_id)
        
    async def get_simulation_status(self) -> Dict[str, Any]:
        """Get current simulation status"""
        if not self.simulation_state:
            return {'status': 'not_running'}
            
        market_conditions = await self.market_data_provider.get_market_conditions()
        recent_news = await self.market_data_provider.get_recent_news(5)
        
        return {
            'simulation_id': self.simulation_state.simulation_id,
            'status': 'running' if self.running else 'stopped',
            'elapsed_days': self.simulation_state.elapsed_days,
            'market_regime': self.simulation_state.market_regime.value,
            'economic_cycle': self.simulation_state.economic_cycle,
            'volatility_index': self.simulation_state.volatility_index,
            'market_sentiment': self.simulation_state.market_sentiment,
            'active_agents': len([a for a in self.registered_agents.values() if a['active']]),
            'total_trades': self.simulation_state.total_trades,
            'market_conditions': {
                'regime': market_conditions.regime.value,
                'volatility': market_conditions.volatility_index,
                'sentiment': market_conditions.market_sentiment,
                'volume_trend': market_conditions.volume_trend
            },
            'recent_news': [
                {
                    'headline': news.headline,
                    'sentiment': news.sentiment,
                    'impact': news.impact_magnitude,
                    'timestamp': news.timestamp
                }
                for news in recent_news
            ]
        }
        
    async def _simulation_loop(self):
        """Main simulation loop"""
        while self.running:
            try:
                # Update simulation time
                if self.simulation_state:
                    current_time = time.time()
                    elapsed_seconds = (current_time - self.simulation_state.start_time) * self.sim_config.time_acceleration
                    self.simulation_state.current_time = self.simulation_state.start_time + elapsed_seconds
                    self.simulation_state.elapsed_days = elapsed_seconds / 86400
                    
                    # Check if simulation duration is complete
                    if self.simulation_state.elapsed_days >= self.sim_config.duration_days:
                        logger.info(f"Simulation {self.simulation_state.simulation_id} completed after {self.sim_config.duration_days} days")
                        break
                        
                # Update economic cycle
                await self._update_economic_cycle()
                
                # Process economic events
                await self._process_economic_events()
                
                # Update market conditions
                await self._update_market_conditions()
                
                # Take portfolio snapshots
                await self._take_portfolio_snapshots()
                
                # Sleep based on time acceleration
                sleep_time = 1.0 / self.sim_config.time_acceleration
                await asyncio.sleep(sleep_time)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in simulation loop: {e}")
                await asyncio.sleep(1)
                
        # Simulation completed
        await self.stop_simulation()
        
    async def _generate_economic_events(self):
        """Generate schedule of economic events"""
        current_time = time.time()
        end_time = current_time + (self.sim_config.duration_days * 86400)
        
        # Generate events throughout the simulation period
        event_time = current_time
        while event_time < end_time:
            # Random event every 1-7 days
            event_time += random.uniform(1, 7) * 86400
            
            event_type = random.choice(list(EconomicEvent))
            event_data = {
                'magnitude': random.uniform(0.1, 0.8),
                'duration_hours': random.uniform(1, 24),
                'market_impact': random.uniform(-0.05, 0.05)  # -5% to +5%
            }
            
            self.economic_events.append((event_time, event_type, event_data))
            
        logger.info(f"Generated {len(self.economic_events)} economic events for simulation")
        
    async def _update_economic_cycle(self):
        """Update economic cycle position"""
        if not self.simulation_state:
            return
            
        # Update cycle position (0.0 to 1.0)
        days_per_cycle = self.cycle_length_days
        self.economic_cycle_position = (self.simulation_state.elapsed_days % days_per_cycle) / days_per_cycle
        
        # Determine cycle phase
        if self.economic_cycle_position < 0.25:
            self.simulation_state.economic_cycle = "expansion"
        elif self.economic_cycle_position < 0.5:
            self.simulation_state.economic_cycle = "peak"
        elif self.economic_cycle_position < 0.75:
            self.simulation_state.economic_cycle = "contraction"
        else:
            self.simulation_state.economic_cycle = "trough"

    async def _process_economic_events(self):
        """Process scheduled economic events"""
        if not self.simulation_state:
            return

        current_time = self.simulation_state.current_time

        # Process events that should happen now
        events_to_process = [
            (event_time, event_type, event_data)
            for event_time, event_type, event_data in self.economic_events
            if abs(event_time - current_time) < 3600  # Within 1 hour
        ]

        for event_time, event_type, event_data in events_to_process:
            logger.info(f"Processing economic event: {event_type.value}")

            # Apply event impact to market
            impact = event_data.get('market_impact', 0)
            if abs(impact) > 0.01:  # Significant impact
                # This would affect market data provider
                pass

    async def _update_market_conditions(self):
        """Update overall market conditions"""
        if not self.simulation_state:
            return

        # Update market conditions based on economic cycle
        if self.simulation_state.economic_cycle == "expansion":
            self.simulation_state.market_sentiment = min(1.0, self.simulation_state.market_sentiment + 0.01)
        elif self.simulation_state.economic_cycle == "contraction":
            self.simulation_state.market_sentiment = max(-1.0, self.simulation_state.market_sentiment - 0.01)

        # Update volatility
        vol_change = random.uniform(-0.5, 0.5)
        self.simulation_state.volatility_index = max(10.0, min(50.0, self.simulation_state.volatility_index + vol_change))

    async def _take_portfolio_snapshots(self):
        """Take portfolio snapshots for all agents"""
        for agent_id in self.registered_agents:
            try:
                portfolio = await self.get_portfolio(agent_id)
                if portfolio:
                    # This would be handled by portfolio manager
                    pass
            except Exception as e:
                logger.error(f"Error taking snapshot for {agent_id}: {e}")
