"""
AI Team Coordinator - Manages One Coordinated AI Trading Team
Coordinates multiple AI agents working together as one team with shared portfolio
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)


class AgentRole(Enum):
    """AI Agent roles in the team"""
    TEAM_LEADER = "team_leader"
    MARKET_ANALYST = "market_analyst"
    STRATEGY_DEVELOPER = "strategy_developer"
    RISK_MANAGER = "risk_manager"
    EXECUTION_SPECIALIST = "execution_specialist"
    PERFORMANCE_EVALUATOR = "performance_evaluator"


@dataclass
class TeamDecision:
    """Team decision made collectively"""
    decision_id: str
    decision_type: str  # 'trade', 'hold', 'rebalance'
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    quantity: float
    confidence: float
    reasoning: str
    contributing_agents: List[str]
    timestamp: float
    risk_assessment: Dict[str, float]
    expected_return: float
    
    
@dataclass
class TeamAnalysis:
    """Collective team analysis"""
    analysis_id: str
    symbol: str
    market_outlook: str
    technical_signals: Dict[str, float]
    fundamental_factors: Dict[str, Any]
    risk_factors: List[str]
    opportunities: List[str]
    team_consensus: float  # 0.0 to 1.0
    timestamp: float


class AITeamCoordinator:
    """
    AI Team Coordinator
    
    Manages one coordinated AI trading team where:
    - Team Leader (exaone-deep:32b) coordinates overall strategy
    - Market Analyst (magistral:24b) analyzes market conditions
    - Strategy Developer (am-thinking) creates trading strategies
    - Risk Manager (phi4-reasoning:plus) manages risk
    - Execution Specialist (nemotron-mini:4b) executes trades
    - All agents share ONE portfolio and work together
    """
    
    def __init__(self, config: Dict[str, Any], market_simulator, ai_integration):
        self.config = config
        self.market_simulator = market_simulator
        self.ai_integration = ai_integration
        
        # Team configuration
        self.team_id = "ai_trading_team_001"
        self.shared_portfolio_id = "team_portfolio_001"
        
        # AI Agent connections (using your real Ollama models)
        self.team_agents = {
            AgentRole.TEAM_LEADER: {
                'model': 'exaone-deep:32b',
                'agent_id': 'team_leader_001',
                'responsibilities': ['strategy_coordination', 'decision_making', 'team_management']
            },
            AgentRole.MARKET_ANALYST: {
                'model': 'huihui_ai/magistral-abliterated:24b',
                'agent_id': 'market_analyst_001', 
                'responsibilities': ['market_analysis', 'trend_identification', 'sentiment_analysis']
            },
            AgentRole.STRATEGY_DEVELOPER: {
                'model': 'huihui_ai/am-thinking-abliterate:latest',
                'agent_id': 'strategy_developer_001',
                'responsibilities': ['strategy_creation', 'signal_generation', 'opportunity_identification']
            },
            AgentRole.RISK_MANAGER: {
                'model': 'phi4-reasoning:plus',
                'agent_id': 'risk_manager_001',
                'responsibilities': ['risk_assessment', 'position_sizing', 'portfolio_protection']
            },
            AgentRole.EXECUTION_SPECIALIST: {
                'model': 'nemotron-mini:4b',
                'agent_id': 'execution_specialist_001',
                'responsibilities': ['trade_execution', 'order_optimization', 'market_timing']
            }
        }
        
        # Team state
        self.team_decisions: List[TeamDecision] = []
        self.team_analyses: List[TeamAnalysis] = []
        self.current_strategy: Optional[Dict[str, Any]] = None
        self.team_consensus: Dict[str, float] = {}
        
        # Communication
        self.agent_responses: Dict[str, Dict[str, Any]] = {}
        self.decision_in_progress = False
        
        # State
        self.initialized = False
        self.active = False
        
    async def initialize(self):
        """Initialize the AI team"""
        try:
            logger.info("Initializing AI Trading Team...")
            
            # Create shared portfolio for the team
            await self.market_simulator.register_agent(
                self.shared_portfolio_id, 
                {'team_mode': True, 'initial_cash': 500000}  # $500k for the team
            )
            
            # Connect each AI agent to the system
            for role, agent_info in self.team_agents.items():
                agent_id = agent_info['agent_id']
                
                # Connect agent to AI integration
                await self.ai_integration.connect_agent(
                    agent_id, 
                    self._create_agent_callback(role, agent_id),
                    {
                        'role': role.value,
                        'model': agent_info['model'],
                        'responsibilities': agent_info['responsibilities'],
                        'team_id': self.team_id,
                        'shared_portfolio': self.shared_portfolio_id
                    }
                )
                
                # Subscribe to market data
                await self.ai_integration.subscribe_to_market_data(
                    agent_id, 
                    ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'NVDA', 'META', 'SPY']
                )
                await self.ai_integration.subscribe_to_news(agent_id)
                
            self.initialized = True
            logger.info(f"✓ AI Trading Team initialized with {len(self.team_agents)} agents")
            
        except Exception as e:
            logger.error(f"Failed to initialize AI Team: {e}")
            raise
            
    async def start_team_operations(self):
        """Start coordinated team operations"""
        if not self.initialized:
            await self.initialize()
            
        self.active = True
        
        # Start team coordination loop
        asyncio.create_task(self._team_coordination_loop())
        
        logger.info("✓ AI Trading Team operations started")
        
    def _create_agent_callback(self, role: AgentRole, agent_id: str):
        """Create callback function for each agent"""
        async def agent_callback(message: Dict[str, Any]):
            try:
                await self._handle_agent_message(role, agent_id, message)
            except Exception as e:
                logger.error(f"Error in {agent_id} callback: {e}")
                
        return agent_callback
        
    async def _handle_agent_message(self, role: AgentRole, agent_id: str, message: Dict[str, Any]):
        """Handle messages from team agents"""
        msg_type = message.get('type')
        data = message.get('data', {})
        
        if msg_type == 'market_data':
            await self._process_market_data_for_team(role, agent_id, data)
        elif msg_type == 'news_update':
            await self._process_news_for_team(role, agent_id, data)
        elif msg_type == 'analysis_request':
            await self._request_agent_analysis(role, agent_id, data)
            
    async def _process_market_data_for_team(self, role: AgentRole, agent_id: str, market_data: Dict[str, Any]):
        """Process market data with role-specific focus"""
        symbol = market_data['symbol']
        
        if role == AgentRole.MARKET_ANALYST:
            # Market analyst focuses on trend analysis
            analysis = await self._analyze_market_trends(symbol, market_data)
            await self._share_analysis_with_team(agent_id, analysis)
            
        elif role == AgentRole.RISK_MANAGER:
            # Risk manager focuses on risk assessment
            risk_assessment = await self._assess_position_risk(symbol, market_data)
            await self._share_risk_assessment_with_team(agent_id, risk_assessment)
            
    async def _team_coordination_loop(self):
        """Main team coordination loop"""
        while self.active:
            try:
                # Periodic team decision making
                await self._coordinate_team_decision()
                
                # Update team consensus
                await self._update_team_consensus()
                
                # Review and adjust strategy
                await self._review_team_strategy()
                
                await asyncio.sleep(30)  # Team coordination every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in team coordination loop: {e}")
                await asyncio.sleep(5)
                
    async def _coordinate_team_decision(self):
        """Coordinate a team trading decision"""
        if self.decision_in_progress:
            return
            
        self.decision_in_progress = True
        
        try:
            # Step 1: Team Leader requests analysis from all agents
            await self._request_team_analysis()
            
            # Step 2: Wait for responses (with timeout)
            await asyncio.sleep(10)  # Give agents time to respond
            
            # Step 3: Team Leader makes final decision based on team input
            decision = await self._make_team_decision()
            
            # Step 4: Execute decision if approved by Risk Manager
            if decision and await self._approve_decision(decision):
                await self._execute_team_decision(decision)
                
        finally:
            self.decision_in_progress = False
            
    async def _request_team_analysis(self):
        """Request analysis from all team members"""
        # Get current market conditions
        market_status = await self.market_simulator.get_simulation_status()
        portfolio = await self.market_simulator.get_portfolio(self.shared_portfolio_id)
        
        analysis_request = {
            'type': 'team_analysis_request',
            'market_conditions': market_status,
            'portfolio_state': portfolio,
            'timestamp': time.time()
        }
        
        # Send to each agent based on their role
        for role, agent_info in self.team_agents.items():
            agent_id = agent_info['agent_id']
            
            # Customize request based on role
            role_specific_request = analysis_request.copy()
            role_specific_request['focus_area'] = self._get_role_focus(role)
            
            # This would trigger the agent's AI model to analyze
            await self._send_to_agent(agent_id, role_specific_request)
            
    def _get_role_focus(self, role: AgentRole) -> str:
        """Get focus area for each role"""
        focus_areas = {
            AgentRole.TEAM_LEADER: "overall_strategy_and_coordination",
            AgentRole.MARKET_ANALYST: "market_trends_and_sentiment",
            AgentRole.STRATEGY_DEVELOPER: "trading_opportunities_and_signals",
            AgentRole.RISK_MANAGER: "risk_assessment_and_position_sizing",
            AgentRole.EXECUTION_SPECIALIST: "execution_timing_and_optimization"
        }
        return focus_areas.get(role, "general_analysis")
        
    async def _make_team_decision(self) -> Optional[TeamDecision]:
        """Team Leader makes decision based on team input"""
        # This would use the Team Leader's AI model (exaone-deep:32b)
        # to process all team member inputs and make a decision
        
        # For now, simplified decision logic
        if len(self.agent_responses) >= 3:  # Need input from at least 3 agents
            
            # Simulate team decision making
            decision = TeamDecision(
                decision_id=f"team_decision_{int(time.time())}",
                decision_type="trade",
                symbol="AAPL",  # Example
                action="buy",
                quantity=100,
                confidence=0.75,
                reasoning="Team consensus indicates bullish sentiment with manageable risk",
                contributing_agents=list(self.agent_responses.keys()),
                timestamp=time.time(),
                risk_assessment={'position_risk': 0.3, 'market_risk': 0.4},
                expected_return=0.05
            )
            
            self.team_decisions.append(decision)
            return decision
            
        return None
        
    async def _approve_decision(self, decision: TeamDecision) -> bool:
        """Risk Manager approves the decision"""
        # Risk Manager (phi4-reasoning:plus) evaluates the decision
        
        # Simplified approval logic
        if decision.confidence > 0.6 and decision.risk_assessment['position_risk'] < 0.5:
            logger.info(f"✅ Risk Manager approved decision: {decision.action} {decision.quantity} {decision.symbol}")
            return True
        else:
            logger.warning(f"❌ Risk Manager rejected decision: Risk too high")
            return False
            
    async def _execute_team_decision(self, decision: TeamDecision):
        """Execution Specialist executes the team decision"""
        try:
            # Execute through the shared portfolio
            order_id = await self.market_simulator.place_order(
                self.shared_portfolio_id,
                decision.symbol,
                decision.action,
                decision.quantity,
                "market"
            )
            
            if order_id:
                logger.info(f"🤖 Team executed decision: {decision.action} {decision.quantity} {decision.symbol} (Order: {order_id})")
            else:
                logger.warning(f"❌ Failed to execute team decision")
                
        except Exception as e:
            logger.error(f"Error executing team decision: {e}")
            
    async def get_team_status(self) -> Dict[str, Any]:
        """Get current team status"""
        portfolio = await self.market_simulator.get_portfolio(self.shared_portfolio_id)
        
        return {
            'team_id': self.team_id,
            'active': self.active,
            'agents': {
                role.value: {
                    'agent_id': info['agent_id'],
                    'model': info['model'],
                    'responsibilities': info['responsibilities']
                }
                for role, info in self.team_agents.items()
            },
            'shared_portfolio': portfolio,
            'recent_decisions': self.team_decisions[-5:],  # Last 5 decisions
            'team_consensus': self.team_consensus,
            'current_strategy': self.current_strategy
        }
        
    # Placeholder methods for AI integration
    async def _analyze_market_trends(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Market analyst analyzes trends"""
        return {'trend': 'bullish', 'confidence': 0.7, 'signals': ['momentum', 'volume']}
        
    async def _assess_position_risk(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Risk manager assesses risk"""
        return {'risk_level': 'medium', 'position_size_limit': 0.1, 'stop_loss': 0.05}
        
    async def _share_analysis_with_team(self, agent_id: str, analysis: Dict[str, Any]):
        """Share analysis with team"""
        self.agent_responses[agent_id] = analysis
        
    async def _share_risk_assessment_with_team(self, agent_id: str, assessment: Dict[str, Any]):
        """Share risk assessment with team"""
        self.agent_responses[agent_id] = assessment
        
    async def _send_to_agent(self, agent_id: str, message: Dict[str, Any]):
        """Send message to specific agent"""
        # This would trigger the agent's AI model
        pass
        
    async def _update_team_consensus(self):
        """Update team consensus metrics"""
        # Calculate consensus based on agent responses
        pass
        
    async def _review_team_strategy(self):
        """Review and adjust team strategy"""
        # Team Leader reviews overall strategy
        pass
