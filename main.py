#!/usr/bin/env python3
"""
Advanced Ollama Trading Agent System
Main entry point for the trading system
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Optional

import click
import yaml
from rich.console import Console
from rich.logging import RichHandler

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Import logging utilities
from utils.logging_utils import safe_success, get_status_symbol

from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from teams.team_manager import TeamManager
from communication.message_broker import MessageBroker
from monitoring.system_monitor import SystemMonitor
from data.market_data_manager import MarketDataManager
from analytics.market_analytics import MarketAnalytics
from ml.predictive_models import PredictiveModels
from ml.reinforcement_learning import RLStrategyOptimizer
from ml.ensemble_methods import EnsembleDecisionMaker
from dashboard.metrics_collector import MetricsCollector

# Import new comprehensive modules
from strategies.strategy_manager import StrategyManager
from risk.risk_manager import RiskManager
from execution.execution_engine import ExecutionEngine
from portfolio.portfolio_manager import PortfolioManager
from learning.learning_manager import LearningManager
from database.database_coordinator import DatabaseCoordinator
from analytics.analytics_engine import AdvancedAnalyticsEngine
from api.api_server import APIServer

console = Console()
logger = logging.getLogger(__name__)


class TradingSystem:
    """Main trading system orchestrator"""
    
    def __init__(self, config_path: str = "config/system_config.yaml"):
        self.config_path = config_path
        self.config = None
        self.running = False
        
        # Core components
        self.ollama_hub: Optional[OllamaModelHub] = None
        self.agent_manager: Optional[AgentManager] = None
        self.team_manager: Optional[TeamManager] = None
        self.message_broker: Optional[MessageBroker] = None
        self.system_monitor: Optional[SystemMonitor] = None
        self.market_data_manager: Optional[MarketDataManager] = None

        # New comprehensive trading components
        self.strategy_manager: Optional[StrategyManager] = None
        self.risk_manager: Optional[RiskManager] = None
        self.execution_engine: Optional[ExecutionEngine] = None
        self.portfolio_manager: Optional[PortfolioManager] = None

        # Learning and adaptation components
        self.learning_manager: Optional[LearningManager] = None

        # Database integration components
        self.database_coordinator: Optional[DatabaseCoordinator] = None

        # Advanced analytics components
        self.analytics_engine: Optional[AdvancedAnalyticsEngine] = None

        # API and web interface components
        self.api_server: Optional[APIServer] = None

        # Advanced components
        self.market_analytics: Optional[MarketAnalytics] = None
        self.predictive_models: Optional[PredictiveModels] = None
        self.rl_optimizer: Optional[RLStrategyOptimizer] = None
        self.ensemble_decision_maker: Optional[EnsembleDecisionMaker] = None
        self.metrics_collector: Optional[MetricsCollector] = None
        
    async def load_config(self):
        """Load system configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            logger.info(f"Configuration loaded from {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
            
    def setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO'))
        
        # Setup rich handler for console output
        logging.basicConfig(
            level=level,
            format=log_config.get('format', '%(message)s'),
            datefmt="[%X]",
            handlers=[RichHandler(console=console, rich_tracebacks=True)]
        )
        
        # Setup file handler if enabled
        file_config = log_config.get('handlers', {}).get('file', {})
        if file_config.get('enabled', False):
            file_handler = logging.FileHandler(
                file_config.get('filename', 'logs/trading_system.log')
            )
            file_handler.setLevel(getattr(logging, file_config.get('level', 'DEBUG')))
            file_handler.setFormatter(
                logging.Formatter(log_config.get('format'))
            )
            logging.getLogger().addHandler(file_handler)
            
    async def initialize_components(self):
        """Initialize all system components"""
        logger.info("Initializing system components...")
        
        try:
            # Initialize Ollama Model Hub
            self.ollama_hub = OllamaModelHub(
                base_url=self.config['ollama']['base_url'],
                config=self.config
            )
            await self.ollama_hub.initialize()
            safe_success(logger, f"{get_status_symbol(True)} Ollama Model Hub initialized")

            # Initialize Message Broker
            self.message_broker = MessageBroker(self.config)
            await self.message_broker.initialize()
            safe_success(logger, f"{get_status_symbol(True)} Message Broker initialized")

            # Initialize Database Coordinator FIRST (needed by other components)
            self.database_coordinator = DatabaseCoordinator(self.config)
            await self.database_coordinator.initialize()
            safe_success(logger, f"{get_status_symbol(True)} Database Coordinator initialized")

            # Initialize Market Data Manager
            self.market_data_manager = MarketDataManager(self.config)
            await self.market_data_manager.initialize()
            safe_success(logger, f"{get_status_symbol(True)} Market Data Manager initialized")

            # Initialize Agent Manager
            self.agent_manager = AgentManager(
                ollama_hub=self.ollama_hub,
                message_broker=self.message_broker,
                config=self.config
            )
            await self.agent_manager.initialize()
            safe_success(logger, f"{get_status_symbol(True)} Agent Manager initialized")

            # Initialize Team Manager
            self.team_manager = TeamManager(
                agent_manager=self.agent_manager,
                message_broker=self.message_broker,
                market_data_manager=self.market_data_manager,
                config=self.config
            )
            await self.team_manager.initialize()
            safe_success(logger, f"{get_status_symbol(True)} Team Manager initialized")

            # Initialize System Monitor
            self.system_monitor = SystemMonitor(
                agent_manager=self.agent_manager,
                team_manager=self.team_manager,
                ollama_hub=self.ollama_hub,
                config=self.config
            )
            await self.system_monitor.initialize()
            safe_success(logger, f"{get_status_symbol(True)} System Monitor initialized")

            # Initialize Advanced Analytics
            self.market_analytics = MarketAnalytics(self.config)
            await self.market_analytics.initialize()
            logger.info("✓ Market Analytics initialized")

            # Initialize ML Components
            self.predictive_models = PredictiveModels(self.config)
            await self.predictive_models.initialize()
            logger.info("✓ Predictive Models initialized")

            self.rl_optimizer = RLStrategyOptimizer(self.config)
            await self.rl_optimizer.initialize()
            logger.info("✓ RL Strategy Optimizer initialized")

            self.ensemble_decision_maker = EnsembleDecisionMaker(self.config)
            await self.ensemble_decision_maker.initialize()
            logger.info("✓ Ensemble Decision Maker initialized")

            # Initialize Metrics Collector
            self.metrics_collector = MetricsCollector(self.config)
            await self.metrics_collector.initialize()
            logger.info("✓ Metrics Collector initialized")

            # Initialize comprehensive trading components
            await self._initialize_trading_components()

            # Initialize learning and adaptation components
            await self._initialize_learning_components()

            # Initialize database integration components
            await self._initialize_database_components()

            # Initialize advanced analytics components
            await self._initialize_analytics_components()

            # Initialize API and web interface components
            await self._initialize_api_components()

            logger.info("🚀 All components initialized successfully!")

        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
            
    async def start_system(self):
        """Start the trading system"""
        logger.info("Starting Advanced Ollama Trading Agent System...")
        
        try:
            # Start all components
            start_tasks = [
                self.database_coordinator.start(),  # Start database first
                self.ollama_hub.start(),
                self.message_broker.start(),
                self.market_data_manager.start(),
                self.agent_manager.start(),
                self.team_manager.start(),
                self.system_monitor.start()
            ]

            # Start advanced components
            if self.market_analytics:
                start_tasks.append(self.market_analytics.start())
            if self.metrics_collector:
                start_tasks.append(self.metrics_collector.start())

            # Start comprehensive trading components
            await self._start_trading_components()

            # Start learning, database, analytics, and API components
            await self._start_advanced_components()

            await asyncio.gather(*start_tasks)
            
            self.running = True
            logger.info("🎯 Trading system started successfully!")
            
            # Display system status
            await self.display_system_status()
            
        except Exception as e:
            logger.error(f"Failed to start system: {e}")
            raise
            
    async def stop_system(self):
        """Stop the trading system gracefully"""
        if not self.running:
            return
            
        logger.info("Stopping trading system...")
        self.running = False
        
        try:
            # Stop all components
            stop_tasks = []
            if self.system_monitor:
                stop_tasks.append(self.system_monitor.stop())
            if self.team_manager:
                stop_tasks.append(self.team_manager.stop())
            if self.agent_manager:
                stop_tasks.append(self.agent_manager.stop())
            if self.market_data_manager:
                stop_tasks.append(self.market_data_manager.stop())
            if self.message_broker:
                stop_tasks.append(self.message_broker.stop())
            if self.ollama_hub:
                stop_tasks.append(self.ollama_hub.stop())

            # Stop comprehensive trading components
            await self._stop_trading_components()

            # Stop learning, database, analytics, and API components
            await self._stop_advanced_components()

            await asyncio.gather(*stop_tasks, return_exceptions=True)
            logger.info("✓ Trading system stopped successfully")
            
        except Exception as e:
            logger.error(f"Error during system shutdown: {e}")

    async def _initialize_trading_components(self):
        """Initialize comprehensive trading components"""
        try:
            logger.info("Initializing comprehensive trading components...")

            # Initialize Risk Manager first (needed by other components)
            self.risk_manager = RiskManager(self.config)
            await self.risk_manager.initialize()
            logger.info("✓ Risk Manager initialized")

            # Initialize Execution Engine
            self.execution_engine = ExecutionEngine(self.config)
            await self.execution_engine.initialize()
            logger.info("✓ Execution Engine initialized")

            # Initialize Portfolio Manager
            self.portfolio_manager = PortfolioManager(self.config)
            await self.portfolio_manager.initialize()
            logger.info("✓ Portfolio Manager initialized")

            # Initialize Strategy Manager
            self.strategy_manager = StrategyManager(self.config)
            await self.strategy_manager.initialize()
            logger.info("✓ Strategy Manager initialized")

            # Set up integration points between components
            await self._setup_component_integration()

            logger.info("✓ All trading components initialized and integrated")

        except Exception as e:
            logger.error(f"Failed to initialize trading components: {e}")
            raise

    async def _setup_component_integration(self):
        """Set up integration points between trading components"""
        try:
            # Portfolio Manager integrations
            await self.portfolio_manager.set_integration_points(
                risk_manager=self.risk_manager,
                execution_engine=self.execution_engine,
                strategy_manager=self.strategy_manager
            )

            # Strategy Manager integrations
            await self.strategy_manager.set_integration_points(
                risk_manager=self.risk_manager,
                execution_engine=self.execution_engine,
                portfolio_manager=self.portfolio_manager,
                market_data_manager=self.market_data_manager,
                message_broker=self.message_broker
            )

            # Risk Manager integrations
            await self.risk_manager.set_integration_points(
                portfolio_manager=self.portfolio_manager,
                execution_engine=self.execution_engine,
                strategy_manager=self.strategy_manager
            )

            # Execution Engine integrations
            await self.execution_engine.set_integration_points(
                risk_manager=self.risk_manager,
                portfolio_manager=self.portfolio_manager
            )

            logger.info("✓ Component integration completed")

        except Exception as e:
            logger.error(f"Error setting up component integration: {e}")
            raise

    async def _start_trading_components(self):
        """Start comprehensive trading components"""
        try:
            # Start components in dependency order
            if self.risk_manager:
                await self.risk_manager.start()
                logger.info("✓ Risk Manager started")

            if self.execution_engine:
                await self.execution_engine.start()
                logger.info("✓ Execution Engine started")

            if self.portfolio_manager:
                await self.portfolio_manager.start()
                logger.info("✓ Portfolio Manager started")

            if self.strategy_manager:
                await self.strategy_manager.start()
                logger.info("✓ Strategy Manager started")

            logger.info("✓ All trading components started")

        except Exception as e:
            logger.error(f"Error starting trading components: {e}")
            raise

    async def _stop_trading_components(self):
        """Stop comprehensive trading components"""
        try:
            # Stop components in reverse dependency order
            if self.strategy_manager:
                await self.strategy_manager.stop()
                logger.info("✓ Strategy Manager stopped")

            if self.portfolio_manager:
                await self.portfolio_manager.stop()
                logger.info("✓ Portfolio Manager stopped")

            if self.execution_engine:
                await self.execution_engine.stop()
                logger.info("✓ Execution Engine stopped")

            if self.risk_manager:
                await self.risk_manager.stop()
                logger.info("✓ Risk Manager stopped")

            logger.info("✓ All trading components stopped")

        except Exception as e:
            logger.error(f"Error stopping trading components: {e}")

    async def _start_advanced_components(self):
        """Start learning, database, analytics, and API components"""
        try:
            # Start database coordinator first
            if self.database_coordinator:
                await self.database_coordinator.start()
                logger.info("✓ Database Coordinator started")

            # Start analytics engine
            if self.analytics_engine:
                await self.analytics_engine.start()
                logger.info("✓ Analytics Engine started")

            # Start learning manager
            if self.learning_manager:
                await self.learning_manager.start()
                logger.info("✓ Learning Manager started")

            # Start API server
            if self.api_server:
                await self.api_server.start()
                logger.info("✓ API Server started")

            logger.info("✓ All advanced components started")

        except Exception as e:
            logger.error(f"Error starting advanced components: {e}")
            raise

    async def _stop_advanced_components(self):
        """Stop learning, database, analytics, and API components"""
        try:
            # Stop API server first
            if self.api_server:
                await self.api_server.stop()
                logger.info("✓ API Server stopped")

            # Stop learning manager
            if self.learning_manager:
                await self.learning_manager.stop()
                logger.info("✓ Learning Manager stopped")

            # Stop analytics engine
            if self.analytics_engine:
                await self.analytics_engine.stop()
                logger.info("✓ Analytics Engine stopped")

            # Stop database coordinator last
            if self.database_coordinator:
                await self.database_coordinator.stop()
                logger.info("✓ Database Coordinator stopped")

            logger.info("✓ All advanced components stopped")

        except Exception as e:
            logger.error(f"Error stopping advanced components: {e}")

    async def _initialize_learning_components(self):
        """Initialize learning and adaptation components"""
        try:
            logger.info("Initializing learning and adaptation components...")

            # Initialize Learning Manager
            self.learning_manager = LearningManager(self.config)
            await self.learning_manager.initialize()
            logger.info("✓ Learning Manager initialized")

            # Set up integration points
            await self.learning_manager.set_integration_points(
                strategy_manager=self.strategy_manager,
                risk_manager=self.risk_manager,
                execution_engine=self.execution_engine,
                portfolio_manager=self.portfolio_manager
            )

            logger.info("✓ Learning components initialized and integrated")

        except Exception as e:
            logger.error(f"Failed to initialize learning components: {e}")
            raise

    async def _initialize_database_components(self):
        """Initialize database integration components"""
        try:
            logger.info("Initializing database integration components...")

            # Initialize Database Coordinator
            self.database_coordinator = DatabaseCoordinator(self.config)
            await self.database_coordinator.initialize()
            logger.info("✓ Database Coordinator initialized")

            # Set up integration points with other components
            if self.strategy_manager:
                await self.strategy_manager.set_database_coordinator(self.database_coordinator)
            if self.risk_manager:
                await self.risk_manager.set_database_coordinator(self.database_coordinator)
            if self.execution_engine:
                await self.execution_engine.set_database_coordinator(self.database_coordinator)
            if self.portfolio_manager:
                await self.portfolio_manager.set_database_coordinator(self.database_coordinator)
            if self.learning_manager:
                await self.learning_manager.set_database_coordinator(self.database_coordinator)

            logger.info("✓ Database components initialized and integrated")

        except Exception as e:
            logger.error(f"Failed to initialize database components: {e}")
            raise

    async def _initialize_analytics_components(self):
        """Initialize advanced analytics components"""
        try:
            logger.info("Initializing advanced analytics components...")

            # Initialize Analytics Engine
            self.analytics_engine = AdvancedAnalyticsEngine(self.config)
            await self.analytics_engine.initialize()
            logger.info("✓ Analytics Engine initialized")

            # Set up integration points with other components
            if self.strategy_manager:
                await self.strategy_manager.set_analytics_engine(self.analytics_engine)
            if self.risk_manager:
                await self.risk_manager.set_analytics_engine(self.analytics_engine)
            if self.execution_engine:
                await self.execution_engine.set_analytics_engine(self.analytics_engine)
            if self.portfolio_manager:
                await self.portfolio_manager.set_analytics_engine(self.analytics_engine)

            logger.info("✓ Analytics components initialized and integrated")

        except Exception as e:
            logger.error(f"Failed to initialize analytics components: {e}")
            raise

    async def _initialize_api_components(self):
        """Initialize API and web interface components"""
        try:
            logger.info("Initializing API and web interface components...")

            # Initialize API Server
            self.api_server = APIServer(self.config, self)
            await self.api_server.initialize()
            logger.info("✓ API Server initialized")

            logger.info("✓ API components initialized and integrated")

        except Exception as e:
            logger.error(f"Failed to initialize API components: {e}")
            raise

    async def display_system_status(self):
        """Display current system status"""
        console.print("\n" + "="*60)
        console.print("🤖 ADVANCED OLLAMA TRADING AGENT SYSTEM", style="bold blue")
        console.print("="*60)
        
        if self.ollama_hub:
            models = await self.ollama_hub.get_available_models()
            console.print(f"📊 Available Models: {len(models)}")
            
        if self.agent_manager:
            agents = await self.agent_manager.get_active_agents()
            console.print(f"🤖 Active Agents: {len(agents)}")
            
        if self.team_manager:
            teams = await self.team_manager.get_active_teams()
            console.print(f"👥 Active Teams: {len(teams)}")

        # Display trading component status
        if self.strategy_manager:
            strategies = await self.strategy_manager.get_active_strategies()
            console.print(f"📈 Active Strategies: {len(strategies)}")

        if self.portfolio_manager:
            portfolio_state = await self.portfolio_manager.get_portfolio_state()
            if portfolio_state:
                console.print(f"💰 Portfolio Value: ${portfolio_state['total_value']:,.2f}")
                console.print(f"📊 Positions: {len(portfolio_state['positions'])}")

        if self.execution_engine:
            active_orders = await self.execution_engine.get_active_orders()
            console.print(f"📋 Active Orders: {len(active_orders)}")

        if self.risk_manager:
            risk_status = await self.risk_manager.get_risk_status()
            console.print(f"⚠️  Risk Status: {risk_status.get('overall_status', 'Unknown')}")

        console.print("="*60)
        console.print("System is running. Press Ctrl+C to stop.", style="green")
        console.print("="*60 + "\n")
        
    async def run(self):
        """Main run loop"""
        try:
            await self.load_config()
            self.setup_logging()
            await self.initialize_components()
            await self.start_system()
            
            # Keep running until interrupted
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
        except Exception as e:
            logger.error(f"System error: {e}")
            raise
        finally:
            await self.stop_system()


def signal_handler(signum, frame):
    """Handle system signals"""
    logger.info(f"Received signal {signum}")
    # The main loop will handle the actual shutdown


@click.command()
@click.option('--config', '-c', default='config/system_config.yaml',
              help='Path to configuration file')
@click.option('--debug', '-d', is_flag=True, help='Enable debug mode')
def main(config: str, debug: bool):
    """Advanced Ollama Trading Agent System"""
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and run the trading system
    system = TradingSystem(config_path=config)
    
    try:
        asyncio.run(system.run())
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!", style="yellow")
    except Exception as e:
        console.print(f"\n❌ System error: {e}", style="red")
        sys.exit(1)


if __name__ == "__main__":
    main()
