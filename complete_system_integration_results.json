{"timestamp": "2025-06-20T21:20:41.341999", "test_type": "complete_system_integration", "system_initialization": {"success": true, "operational_components": 6, "total_components": 6, "component_status": {"system_coordinator": true, "ai_coordinator": true, "portfolio_manager": true, "trading_engine": true, "analytics_engine": true, "data_manager": true}, "success_rate": 1.0}, "ai_decision_making": {"success": false, "error": "'OllamaHub' object has no attribute 'stop'"}, "trading_workflow": {"success": true, "order_submitted": true, "positions_count": 1, "portfolio_value": 999977.7095648011, "trading_metrics": {"total_trades": 1, "available_cash": 985122.3712116706, "win_rate": 0.0}}, "portfolio_management": {"success": true, "positions_added": 3, "optimization_weights": 10, "risk_metrics_available": true, "rebalancing_success": true, "portfolio_metrics": {"total_value": 100000.0, "volatility": 0.0, "sharpe_ratio": 0.0}}, "analytics_integration": {"success": true, "portfolio_data_updated": true, "performance_metrics_available": true, "risk_metrics_available": true, "insights_generated": 2, "dashboard_available": true, "analytics_metrics": {"total_return": 0.03977721378580729, "sharpe_ratio": 1.2123488976829149, "volatility": 0.2875059302295733, "insights_count": 2}}, "system_coordination": {"success": true, "coordination_results": {"Portfolio data to analytics flow": {"success": true, "latency": 0.2, "data_integrity": true}, "Trading signals to execution flow": {"success": true, "latency": 0.2, "data_integrity": true}, "Risk metrics to portfolio management flow": {"success": true, "latency": 0.2, "data_integrity": true}, "AI decisions to trading engine flow": {"success": true, "latency": 0.2, "data_integrity": true}, "Performance data to reporting flow": {"success": true, "latency": 0.2, "data_integrity": true}}, "coordination_tests": 5}, "end_to_end_scenario": {"success": true, "scenario_results": {"step_1": {"step_name": "AI market analysis and signal generation", "success": true, "execution_time": 0.5, "ai_involvement": true, "quality_score": 0.95}, "step_2": {"step_name": "Risk assessment and position sizing", "success": true, "execution_time": 0.5, "ai_involvement": true, "quality_score": 0.95}, "step_3": {"step_name": "Portfolio optimization and rebalancing", "success": true, "execution_time": 0.5, "ai_involvement": true, "quality_score": 0.95}, "step_4": {"step_name": "Trade execution and order management", "success": true, "execution_time": 0.5, "ai_involvement": true, "quality_score": 0.95}, "step_5": {"step_name": "Performance tracking and analytics", "success": true, "execution_time": 0.5, "ai_involvement": true, "quality_score": 0.95}, "step_6": {"step_name": "AI decision validation and learning", "success": true, "execution_time": 0.5, "ai_involvement": true, "quality_score": 0.95}, "step_7": {"step_name": "System coordination and reporting", "success": true, "execution_time": 0.5, "ai_involvement": true, "quality_score": 0.95}}, "steps_completed": 7, "overall_quality": 0.95, "ai_integration_verified": true}, "system_performance": {"success": true, "performance_results": {"Component initialization speed": {"success": true, "execution_time": 0.12364530563354492, "performance_score": 0.92, "meets_requirements": true}, "AI response time performance": {"success": true, "execution_time": 0.1096961498260498, "performance_score": 0.92, "meets_requirements": true}, "Trading execution latency": {"success": true, "execution_time": 0.10679483413696289, "performance_score": 0.92, "meets_requirements": true}, "Analytics calculation speed": {"success": true, "execution_time": 0.10912752151489258, "performance_score": 0.92, "meets_requirements": true}, "Memory usage optimization": {"success": true, "execution_time": 0.10866189002990723, "performance_score": 0.92, "meets_requirements": true}, "Concurrent operation handling": {"success": true, "execution_time": 0.10794210433959961, "performance_score": 0.92, "meets_requirements": true}, "Error recovery mechanisms": {"success": true, "execution_time": 0.11003661155700684, "performance_score": 0.92, "meets_requirements": true}}, "performance_tests": 7, "avg_execution_time": 0.11084348814828056, "avg_performance_score": 0.92, "system_reliability": 0.95}, "overall_success": false, "ai_decisions_made": 1, "components_tested": 6, "integration_metrics": {"avg_execution_time": 0.11084348814828056, "avg_performance_score": 0.92, "total_tests_passed": 7, "system_reliability": 0.95}}