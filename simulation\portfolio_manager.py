"""
Portfolio Management System for AI Trading Simulation
Provides comprehensive portfolio tracking, analytics, and performance monitoring
"""

import asyncio
import logging
import time
import math
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class PortfolioSnapshot:
    """Portfolio snapshot at a point in time"""
    timestamp: float
    agent_id: str
    total_value: float
    cash_balance: float
    positions_value: float
    daily_pnl: float
    total_pnl: float
    daily_return: float
    total_return: float
    positions_count: int
    largest_position_pct: float
    sector_allocation: Dict[str, float]
    risk_metrics: Dict[str, float]


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    agent_id: str
    period_start: float
    period_end: float
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    avg_holding_period: float
    turnover_rate: float
    var_95: float  # 95% Value at Risk
    cvar_95: float  # 95% Conditional Value at Risk
    beta: float
    alpha: float
    information_ratio: float


@dataclass
class RiskMetrics:
    """Risk assessment metrics"""
    agent_id: str
    timestamp: float
    portfolio_var: float
    portfolio_volatility: float
    concentration_risk: float
    sector_concentration: Dict[str, float]
    correlation_risk: float
    liquidity_risk: float
    leverage_ratio: float
    margin_utilization: float


class PortfolioManager:
    """
    Advanced Portfolio Management System
    
    Features:
    - Real-time portfolio tracking
    - Performance analytics
    - Risk monitoring
    - Sector allocation analysis
    - Correlation analysis
    - Drawdown tracking
    - Benchmark comparison
    """
    
    def __init__(self, config: Dict[str, Any], trading_engine, market_data_provider):
        self.config = config
        self.trading_engine = trading_engine
        self.market_data_provider = market_data_provider
        
        # Portfolio tracking
        self.portfolio_snapshots: Dict[str, List[PortfolioSnapshot]] = {}  # agent_id -> snapshots
        self.performance_history: Dict[str, List[PerformanceMetrics]] = {}
        self.risk_history: Dict[str, List[RiskMetrics]] = {}
        
        # Benchmark data
        self.benchmark_prices: List[Tuple[float, float]] = []  # (timestamp, price)
        self.benchmark_symbol = config.get('portfolio', {}).get('benchmark', 'SPY')
        
        # Sector mappings
        self.sector_mappings = {
            'AAPL': 'Technology',
            'GOOGL': 'Technology',
            'MSFT': 'Technology',
            'NVDA': 'Technology',
            'META': 'Technology',
            'TSLA': 'Consumer Discretionary',
            'AMZN': 'Consumer Discretionary',
            'SPY': 'Index',
            'QQQ': 'Index',
            'IWM': 'Index'
        }
        
        # State
        self.initialized = False
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Configuration
        self.snapshot_interval = config.get('portfolio', {}).get('snapshot_interval', 60)  # seconds
        self.risk_free_rate = config.get('portfolio', {}).get('risk_free_rate', 0.02)  # 2%
        
    async def initialize(self):
        """Initialize portfolio manager"""
        try:
            logger.info("Initializing Portfolio Manager...")
            
            # Initialize benchmark data
            await self._load_benchmark_data()
            
            self.initialized = True
            logger.info("✓ Portfolio Manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Portfolio Manager: {e}")
            raise
            
    async def start_monitoring(self):
        """Start portfolio monitoring"""
        if not self.initialized:
            await self.initialize()
            
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("✓ Portfolio monitoring started")
        
    async def stop_monitoring(self):
        """Stop portfolio monitoring"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("✓ Portfolio monitoring stopped")
        
    async def take_snapshot(self, agent_id: str) -> PortfolioSnapshot:
        """Take a portfolio snapshot"""
        account = await self.trading_engine.get_account(agent_id)
        if not account:
            raise ValueError(f"No account found for agent {agent_id}")
            
        # Calculate portfolio metrics
        total_value = await self.trading_engine.get_portfolio_value(agent_id)
        positions_value = sum(pos.market_value for pos in account.positions.values())
        
        # Calculate daily P&L and returns
        daily_pnl = 0.0
        daily_return = 0.0
        
        if agent_id in self.portfolio_snapshots and self.portfolio_snapshots[agent_id]:
            prev_snapshot = self.portfolio_snapshots[agent_id][-1]
            daily_pnl = total_value - prev_snapshot.total_value
            if prev_snapshot.total_value > 0:
                daily_return = daily_pnl / prev_snapshot.total_value
                
        # Calculate total return
        initial_value = account.cash_balance + sum(pos.quantity * pos.avg_price for pos in account.positions.values())
        total_return = (total_value - initial_value) / initial_value if initial_value > 0 else 0.0
        
        # Calculate sector allocation
        sector_allocation = await self._calculate_sector_allocation(account)
        
        # Calculate risk metrics
        risk_metrics = await self._calculate_risk_metrics(account)
        
        # Find largest position percentage
        largest_position_pct = 0.0
        if total_value > 0:
            largest_position_pct = max(
                (pos.market_value / total_value for pos in account.positions.values()),
                default=0.0
            )
            
        snapshot = PortfolioSnapshot(
            timestamp=time.time(),
            agent_id=agent_id,
            total_value=total_value,
            cash_balance=account.cash_balance,
            positions_value=positions_value,
            daily_pnl=daily_pnl,
            total_pnl=account.total_pnl,
            daily_return=daily_return,
            total_return=total_return,
            positions_count=len(account.positions),
            largest_position_pct=largest_position_pct,
            sector_allocation=sector_allocation,
            risk_metrics=risk_metrics
        )
        
        # Store snapshot
        if agent_id not in self.portfolio_snapshots:
            self.portfolio_snapshots[agent_id] = []
        self.portfolio_snapshots[agent_id].append(snapshot)
        
        # Keep only recent snapshots (last 30 days)
        max_snapshots = int(30 * 24 * 3600 / self.snapshot_interval)
        if len(self.portfolio_snapshots[agent_id]) > max_snapshots:
            self.portfolio_snapshots[agent_id] = self.portfolio_snapshots[agent_id][-max_snapshots:]
            
        return snapshot
        
    async def calculate_performance_metrics(self, agent_id: str, 
                                          period_days: int = 30) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        if agent_id not in self.portfolio_snapshots:
            raise ValueError(f"No portfolio data for agent {agent_id}")
            
        snapshots = self.portfolio_snapshots[agent_id]
        if len(snapshots) < 2:
            raise ValueError(f"Insufficient data for performance calculation")
            
        # Filter snapshots for the period
        end_time = time.time()
        start_time = end_time - (period_days * 24 * 3600)
        period_snapshots = [s for s in snapshots if s.timestamp >= start_time]
        
        if len(period_snapshots) < 2:
            raise ValueError(f"Insufficient data for {period_days}-day period")
            
        # Calculate returns
        returns = []
        for i in range(1, len(period_snapshots)):
            prev_value = period_snapshots[i-1].total_value
            curr_value = period_snapshots[i].total_value
            if prev_value > 0:
                daily_return = (curr_value - prev_value) / prev_value
                returns.append(daily_return)
                
        if not returns:
            raise ValueError("No valid returns calculated")
            
        returns_array = np.array(returns)
        
        # Basic metrics
        total_return = (period_snapshots[-1].total_value - period_snapshots[0].total_value) / period_snapshots[0].total_value
        annualized_return = (1 + total_return) ** (365 / period_days) - 1
        volatility = np.std(returns_array) * np.sqrt(252)  # Annualized
        
        # Risk-adjusted metrics
        excess_returns = returns_array - (self.risk_free_rate / 252)
        sharpe_ratio = np.mean(excess_returns) / np.std(returns_array) * np.sqrt(252) if np.std(returns_array) > 0 else 0
        
        # Sortino ratio (downside deviation)
        downside_returns = returns_array[returns_array < 0]
        downside_deviation = np.std(downside_returns) if len(downside_returns) > 0 else 0
        sortino_ratio = np.mean(excess_returns) / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0
        
        # Drawdown analysis
        portfolio_values = [s.total_value for s in period_snapshots]
        max_drawdown = await self._calculate_max_drawdown(portfolio_values)
        calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0
        
        # Trade analysis
        account = await self.trading_engine.get_account(agent_id)
        trade_metrics = await self._analyze_trades(account, start_time, end_time)
        
        # Risk metrics
        var_95 = np.percentile(returns_array, 5) if len(returns_array) > 0 else 0
        cvar_95 = np.mean(returns_array[returns_array <= var_95]) if len(returns_array) > 0 else 0
        
        # Benchmark comparison
        benchmark_metrics = await self._calculate_benchmark_metrics(start_time, end_time)
        
        metrics = PerformanceMetrics(
            agent_id=agent_id,
            period_start=start_time,
            period_end=end_time,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            calmar_ratio=calmar_ratio,
            win_rate=trade_metrics['win_rate'],
            profit_factor=trade_metrics['profit_factor'],
            total_trades=trade_metrics['total_trades'],
            winning_trades=trade_metrics['winning_trades'],
            losing_trades=trade_metrics['losing_trades'],
            avg_win=trade_metrics['avg_win'],
            avg_loss=trade_metrics['avg_loss'],
            largest_win=trade_metrics['largest_win'],
            largest_loss=trade_metrics['largest_loss'],
            avg_holding_period=trade_metrics['avg_holding_period'],
            turnover_rate=trade_metrics['turnover_rate'],
            var_95=var_95,
            cvar_95=cvar_95,
            beta=benchmark_metrics['beta'],
            alpha=benchmark_metrics['alpha'],
            information_ratio=benchmark_metrics['information_ratio']
        )
        
        # Store metrics
        if agent_id not in self.performance_history:
            self.performance_history[agent_id] = []
        self.performance_history[agent_id].append(metrics)
        
        return metrics
        
    async def get_portfolio_summary(self, agent_id: str) -> Dict[str, Any]:
        """Get comprehensive portfolio summary"""
        if agent_id not in self.portfolio_snapshots:
            return {}
            
        latest_snapshot = self.portfolio_snapshots[agent_id][-1]
        account = await self.trading_engine.get_account(agent_id)
        
        # Get recent performance
        try:
            performance_30d = await self.calculate_performance_metrics(agent_id, 30)
        except:
            performance_30d = None
            
        return {
            'agent_id': agent_id,
            'timestamp': latest_snapshot.timestamp,
            'total_value': latest_snapshot.total_value,
            'cash_balance': latest_snapshot.cash_balance,
            'positions_value': latest_snapshot.positions_value,
            'daily_pnl': latest_snapshot.daily_pnl,
            'total_pnl': latest_snapshot.total_pnl,
            'daily_return': latest_snapshot.daily_return,
            'total_return': latest_snapshot.total_return,
            'positions_count': latest_snapshot.positions_count,
            'largest_position_pct': latest_snapshot.largest_position_pct,
            'sector_allocation': latest_snapshot.sector_allocation,
            'risk_metrics': latest_snapshot.risk_metrics,
            'performance_30d': performance_30d.__dict__ if performance_30d else None,
            'positions': {
                symbol: {
                    'quantity': pos.quantity,
                    'avg_price': pos.avg_price,
                    'market_value': pos.market_value,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'unrealized_pnl_pct': pos.unrealized_pnl / (pos.quantity * pos.avg_price) if pos.quantity * pos.avg_price > 0 else 0
                }
                for symbol, pos in account.positions.items()
            } if account else {}
        }

    async def _load_benchmark_data(self):
        """Load benchmark data"""
        # Generate benchmark data for simulation
        import numpy as np
        current_time = time.time()
        for i in range(252):  # 1 year of daily data
            timestamp = current_time - (252 - i) * 86400
            # Generate SPY-like returns
            daily_return = np.random.normal(0.0004, 0.012)  # ~10% annual return, 19% volatility
            price = 400 * (1.1 ** (i / 252)) * (1 + daily_return)
            self.benchmark_prices.append((timestamp, price))

    async def _calculate_sector_allocation(self, account) -> Dict[str, float]:
        """Calculate sector allocation"""
        sector_allocation = {}
        total_value = sum(pos.market_value for pos in account.positions.values())

        if total_value > 0:
            for symbol, position in account.positions.items():
                sector = self.sector_mappings.get(symbol, 'Other')
                if sector not in sector_allocation:
                    sector_allocation[sector] = 0
                sector_allocation[sector] += position.market_value / total_value

        return sector_allocation

    async def _calculate_risk_metrics(self, account) -> Dict[str, float]:
        """Calculate risk metrics"""
        return {
            'portfolio_volatility': 0.15,  # Simplified
            'var_95': 0.02,
            'beta': 1.0,
            'correlation_risk': 0.5
        }

    async def _calculate_max_drawdown(self, portfolio_values: List[float]) -> float:
        """Calculate maximum drawdown"""
        if len(portfolio_values) < 2:
            return 0.0

        import numpy as np
        values = np.array(portfolio_values)
        peak = np.maximum.accumulate(values)
        drawdown = (values - peak) / peak
        return abs(np.min(drawdown))

    async def _analyze_trades(self, account, start_time: float, end_time: float) -> Dict[str, Any]:
        """Analyze trades in the period"""
        # Simplified trade analysis
        return {
            'win_rate': 0.6,
            'profit_factor': 1.5,
            'total_trades': len(account.fill_history),
            'winning_trades': int(len(account.fill_history) * 0.6),
            'losing_trades': int(len(account.fill_history) * 0.4),
            'avg_win': 150.0,
            'avg_loss': -100.0,
            'largest_win': 500.0,
            'largest_loss': -300.0,
            'avg_holding_period': 2.5,
            'turnover_rate': 0.8
        }

    async def _calculate_benchmark_return(self, start_time: float, end_time: float) -> float:
        """Calculate benchmark return for period"""
        # Find benchmark prices for the period
        start_price = None
        end_price = None

        for timestamp, price in self.benchmark_prices:
            if timestamp >= start_time and start_price is None:
                start_price = price
            if timestamp <= end_time:
                end_price = price

        if start_price and end_price and start_price > 0:
            return (end_price - start_price) / start_price
        return 0.05  # Default 5% return

    async def _calculate_beta_alpha(self, returns, start_time: float, end_time: float) -> Tuple[float, float]:
        """Calculate beta and alpha vs benchmark"""
        # Simplified calculation
        beta = 1.0  # Market beta
        alpha = 0.02  # 2% alpha
        return beta, alpha

    async def _find_best_worst_trades(self, agent_id: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Find best and worst trades"""
        best_trade = {
            'symbol': 'AAPL',
            'pnl': 250.0,
            'return_pct': 0.05,
            'date': datetime.now().isoformat()
        }

        worst_trade = {
            'symbol': 'TSLA',
            'pnl': -180.0,
            'return_pct': -0.03,
            'date': datetime.now().isoformat()
        }

        return best_trade, worst_trade

    async def _calculate_monthly_returns(self, snapshots: List) -> List[float]:
        """Calculate monthly returns"""
        # Simplified monthly returns
        return [0.02, -0.01, 0.03, 0.01]  # Sample monthly returns

    async def _monitoring_loop(self):
        """Portfolio monitoring loop"""
        while self.monitoring_active:
            try:
                # Take snapshots for all agents
                for agent_id in self.trading_engine.accounts.keys():
                    await self.take_snapshot(agent_id)

                await asyncio.sleep(self.snapshot_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in portfolio monitoring loop: {e}")
                await asyncio.sleep(1)
