{"validation_id": "validation_**********_e200bce1", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.8016753573948853, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.8175073235607666, "error_count": 0, "warnings": ["Integration issues in system_coordinator"], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8846986957868825, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8321889597098316, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.8754932776558977, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.8463393859097613, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8069126417867939, "error_count": 0, "warnings": ["Integration issues in execution_engine"], "dependencies_met": "False"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8597442415592623, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8681859498408271, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.8564555962074517, "error_count": 0, "warnings": ["Integration issues in strategy_manager"], "dependencies_met": "False"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8398172254821517, "error_count": 0, "warnings": ["Integration issues in competitive_framework"], "dependencies_met": "False"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8261038247534733, "error_count": 0, "warnings": ["Integration issues in tournament_framework"], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8586417276901336, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.8231668968416437, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.8427938171853799, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8826790021118566, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8042090603593923, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.9104199728047584, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.9216098599421338, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8939388466173286, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.7850203673168391, "error_count": 0, "warnings": [], "dependencies_met": "False"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.8751155217701657, "data_manager": 0.6569632425397259, "analytics_engine": 0.7572436423958286, "ollama_hub": 0.6024780780189747, "execution_engine": 0.8809865386343374, "portfolio_manager": 0.7308494445698676, "risk_manager": 0.7331304402714894, "strategy_manager": 0.7498705862443098, "competitive_framework": 0.7137663251752158, "tournament_framework": 0.874730765680713, "self_improvement_engine": 0.7858925450418972, "regime_adaptation_system": 0.8322996764090169, "performance_optimizer": 0.8786564094117788, "advanced_trading_engine": 0.7220011246727853, "ai_coordinator": 0.8275538353174849, "configuration_manager": 0.7937766628184713, "mock_data_providers": 0.6703877269691314, "paper_trading_engine": 0.6251640623385833, "logging_audit_system": 0.6822077193228799}, "team_manager": {"system_coordinator": 0.6352200462946099, "team_manager": 1.0, "data_manager": 0.9127207056379711, "analytics_engine": 0.7748201536410885, "ollama_hub": 0.8934226477608862, "execution_engine": 0.8113718937996877, "portfolio_manager": 0.7777503210279751, "risk_manager": 0.824747822923427, "strategy_manager": 0.6606950623965557, "competitive_framework": 0.6411817459209708, "tournament_framework": 0.6083339353771361, "self_improvement_engine": 0.7720247104123379, "regime_adaptation_system": 0.8109690484294801, "performance_optimizer": 0.8514655997920455, "advanced_trading_engine": 0.6078683788901714, "ai_coordinator": 0.8455287670485112, "configuration_manager": 0.6418046304934919, "mock_data_providers": 0.6552821209807761, "paper_trading_engine": 0.8287704606983295, "logging_audit_system": 0.7472523669545147}, "data_manager": {"system_coordinator": 0.7324039251951182, "team_manager": 0.6406327782280292, "data_manager": 1.0, "analytics_engine": 0.8822920912802132, "ollama_hub": 0.704030532451565, "execution_engine": 0.6811056484813205, "portfolio_manager": 0.6208072822990001, "risk_manager": 0.8763393296998894, "strategy_manager": 0.6729181886853995, "competitive_framework": 0.7145455694946554, "tournament_framework": 0.6456374926327291, "self_improvement_engine": 0.8041330594226132, "regime_adaptation_system": 0.8867928446669278, "performance_optimizer": 0.8863356828149151, "advanced_trading_engine": 0.6866928080742103, "ai_coordinator": 0.8405007887202354, "configuration_manager": 0.7194978704696131, "mock_data_providers": 0.6129245620053995, "paper_trading_engine": 0.759747809913045, "logging_audit_system": 0.87443449735534}, "analytics_engine": {"system_coordinator": 0.6461829872105831, "team_manager": 0.6990349066190842, "data_manager": 0.888137207754355, "analytics_engine": 1.0, "ollama_hub": 0.6924446407406137, "execution_engine": 0.808986513699727, "portfolio_manager": 0.6041282008497602, "risk_manager": 0.6782825864983258, "strategy_manager": 0.9661667520349735, "competitive_framework": 0.7630562446144447, "tournament_framework": 0.6597868838704741, "self_improvement_engine": 0.7540966717148377, "regime_adaptation_system": 0.8936749656722893, "performance_optimizer": 0.7033856603971643, "advanced_trading_engine": 0.6674942564485613, "ai_coordinator": 0.8003874482010545, "configuration_manager": 0.8815746514922431, "mock_data_providers": 0.779892333956678, "paper_trading_engine": 0.8684131852311665, "logging_audit_system": 0.795816142254445}, "ollama_hub": {"system_coordinator": 0.7546196600726143, "team_manager": 0.8516444652453944, "data_manager": 0.7491015380035242, "analytics_engine": 0.6344949336581639, "ollama_hub": 1.0, "execution_engine": 0.8589067155524917, "portfolio_manager": 0.8054007286114476, "risk_manager": 0.6603729314739709, "strategy_manager": 0.7013824081794228, "competitive_framework": 0.632413434607172, "tournament_framework": 0.7797316591974214, "self_improvement_engine": 0.8378922642611972, "regime_adaptation_system": 0.7224884873643379, "performance_optimizer": 0.7230359972750474, "advanced_trading_engine": 0.8077495511256003, "ai_coordinator": 0.7789099911030865, "configuration_manager": 0.6578645547655331, "mock_data_providers": 0.6523000919745354, "paper_trading_engine": 0.7332058093051893, "logging_audit_system": 0.798740067502861}, "execution_engine": {"system_coordinator": 0.6415325315070302, "team_manager": 0.893943129795181, "data_manager": 0.7813459175898277, "analytics_engine": 0.8889665186467989, "ollama_hub": 0.8461769973105967, "execution_engine": 1.0, "portfolio_manager": 0.9879595975095987, "risk_manager": 0.8347124870674032, "strategy_manager": 0.8487179549014061, "competitive_framework": 0.6037110257346292, "tournament_framework": 0.8697560745983792, "self_improvement_engine": 0.7219713734586289, "regime_adaptation_system": 0.8132609596838298, "performance_optimizer": 0.82787376487424, "advanced_trading_engine": 0.6348529313934516, "ai_coordinator": 0.8692331527200758, "configuration_manager": 0.7369805187123797, "mock_data_providers": 0.8793046700922407, "paper_trading_engine": 0.7648458352385082, "logging_audit_system": 0.6244084511137096}, "portfolio_manager": {"system_coordinator": 0.6407302150682609, "team_manager": 0.717201944551549, "data_manager": 0.6438559848053288, "analytics_engine": 0.8963118654861025, "ollama_hub": 0.6431662540699904, "execution_engine": 0.7555681130065202, "portfolio_manager": 1.0, "risk_manager": 0.7782673301642256, "strategy_manager": 0.7008714015612162, "competitive_framework": 0.8626589070370148, "tournament_framework": 0.7596622145581492, "self_improvement_engine": 0.8632826092584658, "regime_adaptation_system": 0.665355835571584, "performance_optimizer": 0.6889808066841927, "advanced_trading_engine": 0.7055652094549794, "ai_coordinator": 0.899479601275368, "configuration_manager": 0.8828406777534218, "mock_data_providers": 0.6872911116438549, "paper_trading_engine": 0.8354205675503741, "logging_audit_system": 0.8031076725117651}, "risk_manager": {"system_coordinator": 0.7946682760721212, "team_manager": 0.6739466655105881, "data_manager": 0.8756146481398948, "analytics_engine": 0.6981533404301904, "ollama_hub": 0.6368915961829458, "execution_engine": 0.8341905290679795, "portfolio_manager": 0.7643553802462453, "risk_manager": 1.0, "strategy_manager": 0.8492009874359983, "competitive_framework": 0.6290424174259986, "tournament_framework": 0.8441408019074651, "self_improvement_engine": 0.6558710650384743, "regime_adaptation_system": 0.8568928403629443, "performance_optimizer": 0.6220191409539271, "advanced_trading_engine": 0.6578074795261378, "ai_coordinator": 0.7257801837292833, "configuration_manager": 0.8271943182617518, "mock_data_providers": 0.8266699136667054, "paper_trading_engine": 0.794310353480692, "logging_audit_system": 0.6967721671423379}, "strategy_manager": {"system_coordinator": 0.7054476464779434, "team_manager": 0.7933715147519536, "data_manager": 0.6704485761221481, "analytics_engine": 0.6464128255766156, "ollama_hub": 0.6934700397419579, "execution_engine": 0.6763683869896726, "portfolio_manager": 0.8263231374955599, "risk_manager": 0.7545241822439913, "strategy_manager": 1.0, "competitive_framework": 0.7547306567571398, "tournament_framework": 0.8807051831611389, "self_improvement_engine": 0.7362967942692021, "regime_adaptation_system": 0.7788172656237715, "performance_optimizer": 0.7305317675251154, "advanced_trading_engine": 0.7337163255590695, "ai_coordinator": 0.6670110219555138, "configuration_manager": 0.7483031562499386, "mock_data_providers": 0.6847113922941964, "paper_trading_engine": 0.6990499792914582, "logging_audit_system": 0.6029295258491778}, "competitive_framework": {"system_coordinator": 0.8394973121288858, "team_manager": 0.7683575782326844, "data_manager": 0.8189550727985285, "analytics_engine": 0.761054913217266, "ollama_hub": 0.7655321193512004, "execution_engine": 0.8373886234772316, "portfolio_manager": 0.7817696565052613, "risk_manager": 0.799113910500941, "strategy_manager": 0.7623392605999455, "competitive_framework": 1.0, "tournament_framework": 0.711393798664315, "self_improvement_engine": 0.7648449294962258, "regime_adaptation_system": 0.6500896911532997, "performance_optimizer": 0.6360150922078855, "advanced_trading_engine": 0.6738770699044506, "ai_coordinator": 0.7244622153764265, "configuration_manager": 0.6272497127335671, "mock_data_providers": 0.6433214438416898, "paper_trading_engine": 0.7458272256430182, "logging_audit_system": 0.8841566430262671}, "tournament_framework": {"system_coordinator": 0.8046630684370188, "team_manager": 0.646466692246591, "data_manager": 0.7088415214388781, "analytics_engine": 0.6931757813853779, "ollama_hub": 0.7893078324430932, "execution_engine": 0.870067515669801, "portfolio_manager": 0.6130017617022723, "risk_manager": 0.868612453288807, "strategy_manager": 0.691475171583212, "competitive_framework": 0.8436570773951066, "tournament_framework": 1.0, "self_improvement_engine": 0.8692294444672524, "regime_adaptation_system": 0.7215658608551998, "performance_optimizer": 0.7896939152512614, "advanced_trading_engine": 0.6265821360798632, "ai_coordinator": 0.8227593728055849, "configuration_manager": 0.7806057126468542, "mock_data_providers": 0.6159426977790201, "paper_trading_engine": 0.6252401119973259, "logging_audit_system": 0.6195076557987345}, "self_improvement_engine": {"system_coordinator": 0.8833961840488123, "team_manager": 0.65210348400321, "data_manager": 0.6338265411872228, "analytics_engine": 0.8550032870162927, "ollama_hub": 0.7755594389004262, "execution_engine": 0.8801907029926638, "portfolio_manager": 0.7282494207955822, "risk_manager": 0.6706370888238348, "strategy_manager": 0.7342611631523375, "competitive_framework": 0.8645672485971808, "tournament_framework": 0.8956905677350256, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.6827830435977353, "performance_optimizer": 0.8657873511128952, "advanced_trading_engine": 0.6219242495386319, "ai_coordinator": 0.8281101565176348, "configuration_manager": 0.7753561077852118, "mock_data_providers": 0.8585239208232713, "paper_trading_engine": 0.8847542757648746, "logging_audit_system": 0.747200270800948}, "regime_adaptation_system": {"system_coordinator": 0.6805231710168318, "team_manager": 0.7321274399351879, "data_manager": 0.8999374026827269, "analytics_engine": 0.793803672659342, "ollama_hub": 0.8179449480566652, "execution_engine": 0.8602476497346316, "portfolio_manager": 0.808403025186262, "risk_manager": 0.7230225967764534, "strategy_manager": 0.8096149347239081, "competitive_framework": 0.6094979442223177, "tournament_framework": 0.8537617634492125, "self_improvement_engine": 0.7835820834355793, "regime_adaptation_system": 1.0, "performance_optimizer": 0.6518097860964182, "advanced_trading_engine": 0.7183658297193826, "ai_coordinator": 0.8255725490899777, "configuration_manager": 0.7414348690877923, "mock_data_providers": 0.817133224608799, "paper_trading_engine": 0.7101127593279796, "logging_audit_system": 0.802384083614064}, "performance_optimizer": {"system_coordinator": 0.697198884466491, "team_manager": 0.6089353050309738, "data_manager": 0.643879537535333, "analytics_engine": 0.8885572919533969, "ollama_hub": 0.8568375633343988, "execution_engine": 0.7547545360130983, "portfolio_manager": 0.7797117976201899, "risk_manager": 0.8155802493732921, "strategy_manager": 0.7818029808247525, "competitive_framework": 0.6050389427344327, "tournament_framework": 0.6156923781740689, "self_improvement_engine": 0.7872166114237975, "regime_adaptation_system": 0.8133850004273904, "performance_optimizer": 1.0, "advanced_trading_engine": 0.8744424690527391, "ai_coordinator": 0.8480572615186115, "configuration_manager": 0.7497429606718733, "mock_data_providers": 0.813240408903979, "paper_trading_engine": 0.7054153246690084, "logging_audit_system": 0.8310304204668544}, "advanced_trading_engine": {"system_coordinator": 0.68032967574147, "team_manager": 0.717220698116057, "data_manager": 0.6362625454569061, "analytics_engine": 0.6190724663175248, "ollama_hub": 0.8385343325672552, "execution_engine": 0.6480414249727804, "portfolio_manager": 0.770627452026163, "risk_manager": 0.6205572063219175, "strategy_manager": 0.7830646256698639, "competitive_framework": 0.6676878934456643, "tournament_framework": 0.719924356185593, "self_improvement_engine": 0.6018934467623177, "regime_adaptation_system": 0.7789488501845858, "performance_optimizer": 0.8552702793777165, "advanced_trading_engine": 1.0, "ai_coordinator": 0.7322511025603906, "configuration_manager": 0.7702953182815471, "mock_data_providers": 0.6266713796030949, "paper_trading_engine": 0.896865197289005, "logging_audit_system": 0.8373614112404149}, "ai_coordinator": {"system_coordinator": 0.6451277145769675, "team_manager": 0.7033847623928194, "data_manager": 0.6415374983800687, "analytics_engine": 0.8987790145112682, "ollama_hub": 0.6004202346663207, "execution_engine": 0.6487382005680107, "portfolio_manager": 0.7378914465081925, "risk_manager": 0.6944133683797152, "strategy_manager": 0.8526648729538338, "competitive_framework": 0.6299870803572621, "tournament_framework": 0.717875731720749, "self_improvement_engine": 0.6948909346579082, "regime_adaptation_system": 0.6238653212845894, "performance_optimizer": 0.842001616061356, "advanced_trading_engine": 0.879599211855573, "ai_coordinator": 1.0, "configuration_manager": 0.7420452016274581, "mock_data_providers": 0.6807314796974833, "paper_trading_engine": 0.7839515234260399, "logging_audit_system": 0.6553600531365494}, "configuration_manager": {"system_coordinator": 0.6867851195598257, "team_manager": 0.8017574225689386, "data_manager": 0.6023212698114749, "analytics_engine": 0.7659967446986374, "ollama_hub": 0.8487254438745013, "execution_engine": 0.6359937792457847, "portfolio_manager": 0.7681280233042065, "risk_manager": 0.7970440105284186, "strategy_manager": 0.7524559907912745, "competitive_framework": 0.857241681783352, "tournament_framework": 0.8847257597613714, "self_improvement_engine": 0.682782303540655, "regime_adaptation_system": 0.6486718367556054, "performance_optimizer": 0.7528464314987457, "advanced_trading_engine": 0.6544228544560028, "ai_coordinator": 0.8000421350575896, "configuration_manager": 1.0, "mock_data_providers": 0.713808522904835, "paper_trading_engine": 0.7089450104487786, "logging_audit_system": 0.7008150090583148}, "mock_data_providers": {"system_coordinator": 0.6373311950922032, "team_manager": 0.7605008768306952, "data_manager": 0.6329936840831181, "analytics_engine": 0.8182790062785209, "ollama_hub": 0.7825374923992782, "execution_engine": 0.7788794631961414, "portfolio_manager": 0.7297641790260033, "risk_manager": 0.8999910244392617, "strategy_manager": 0.7920615387291907, "competitive_framework": 0.6115455985800194, "tournament_framework": 0.874344225392957, "self_improvement_engine": 0.6702409823154315, "regime_adaptation_system": 0.6099325148462551, "performance_optimizer": 0.6893336908326262, "advanced_trading_engine": 0.6639595388036932, "ai_coordinator": 0.7961743729751223, "configuration_manager": 0.8670727273743581, "mock_data_providers": 1.0, "paper_trading_engine": 0.8974933928912016, "logging_audit_system": 0.7833195506407268}, "paper_trading_engine": {"system_coordinator": 0.6307910123008432, "team_manager": 0.6262793403156895, "data_manager": 0.8416475025419776, "analytics_engine": 0.8669016059525954, "ollama_hub": 0.7225552685546123, "execution_engine": 0.8890742626912853, "portfolio_manager": 0.7572027281954813, "risk_manager": 0.8672193246602398, "strategy_manager": 0.8806021966185744, "competitive_framework": 0.6961243410133443, "tournament_framework": 0.864341774985597, "self_improvement_engine": 0.6237178346909876, "regime_adaptation_system": 0.748925140273923, "performance_optimizer": 0.7944688147042498, "advanced_trading_engine": 0.6592750974590665, "ai_coordinator": 0.7459510020719635, "configuration_manager": 0.8572526103094796, "mock_data_providers": 0.7169871792693394, "paper_trading_engine": 1.0, "logging_audit_system": 0.7400269113062523}, "logging_audit_system": {"system_coordinator": 0.7296334273069816, "team_manager": 0.6651540424967473, "data_manager": 0.7305496821479599, "analytics_engine": 0.7330925944158939, "ollama_hub": 0.6995561928834189, "execution_engine": 0.8246635872822707, "portfolio_manager": 0.8716249471209359, "risk_manager": 0.7248702726697446, "strategy_manager": 0.7626869975015651, "competitive_framework": 0.6246904661635665, "tournament_framework": 0.7384795773672337, "self_improvement_engine": 0.6533377604256678, "regime_adaptation_system": 0.8717838075326416, "performance_optimizer": 0.7007426468422793, "advanced_trading_engine": 0.6371424209270882, "ai_coordinator": 0.6940588422089762, "configuration_manager": 0.747199032710881, "mock_data_providers": 0.6265218851647142, "paper_trading_engine": 0.8932083372520714, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.865861633056059, "response_time": 0.9369955906221881, "throughput": 0.6601553047810269, "memory_usage": 0.8867477016630034, "cpu_usage": 0.8320437567720093, "concurrent_operations": 0.608138645655188}, "critical_issues": ["Components with dependency issues: ollama_hub, execution_engine, strategy_manager, competitive_framework, self_improvement_engine, logging_audit_system"], "recommendations": ["Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.4841316}