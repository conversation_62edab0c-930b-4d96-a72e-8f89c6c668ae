"""
Ollama Hub - Real implementation using OllamaModelHub
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
import sys
import os

# Add models directory to path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'models'))

from models.ollama_hub import OllamaModelHub

logger = logging.getLogger(__name__)


class OllamaHub:
    """
    Real Ollama Hub implementation - wrapper around OllamaModelHub
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.models = config.get('models', [])

        # Create real OllamaModelHub instance
        self.ollama_hub = OllamaModelHub(
            base_url=config.get('base_url', 'http://localhost:11434'),
            config=config
        )

        self.initialized = False
        self.available_models = []
        
    async def initialize(self) -> bool:
        """Initialize Ollama Hub"""
        try:
            logger.info("🚀 Initializing Real Ollama Hub...")

            # Initialize the real OllamaModelHub
            await self.ollama_hub.initialize()
            await self.ollama_hub.start()

            # Get available models from real Ollama service
            models_data = await self.ollama_hub.get_available_models()
            self.available_models = [model.get('name', '') for model in models_data if model.get('name')]

            self.initialized = True
            logger.info(f"✅ Real Ollama Hub initialized successfully with {len(self.available_models)} models")
            return True

        except Exception as e:
            logger.error(f"❌ Real Ollama Hub initialization failed: {e}")
            raise RuntimeError(f"Ollama Hub initialization required but failed: {e}")
            
    async def get_available_models(self) -> List[str]:
        """Get available models"""
        try:
            if not self.initialized:
                return []

            # Get fresh model list from real Ollama service
            models_data = await self.ollama_hub.get_available_models()
            self.available_models = [model.get('name', '') for model in models_data if model.get('name')]
            return self.available_models.copy()

        except Exception as e:
            logger.error(f"❌ Failed to get available models: {e}")
            raise RuntimeError(f"Failed to get available models from Ollama: {e}")
            
    async def generate_response(self, prompt: str, model: str = None) -> str:
        """Generate AI response using real Ollama"""
        try:
            if not self.initialized:
                raise RuntimeError("Ollama Hub not initialized")

            if model and model not in self.available_models:
                logger.warning(f"Model {model} not available, using first available model")
                model = self.available_models[0] if self.available_models else None

            if not model:
                raise RuntimeError("No models available for response generation")

            # Deploy model instance for temporary agent
            model_instance = await self.ollama_hub.deploy_model_for_agent(
                agent_name="temp_agent",
                role="general",
                model_name=model
            )

            if not model_instance:
                raise RuntimeError(f"Failed to deploy model {model}")

            # Generate real response using Ollama
            response_data = await model_instance.generate(prompt)

            if response_data.get('success'):
                response = response_data.get('message', {}).get('content', '')
                logger.info(f"🧠 Generated real response using model {model}: {len(response)} characters")
                return response
            else:
                error_msg = response_data.get('error', 'Unknown error')
                raise RuntimeError(f"Model response generation failed: {error_msg}")

        except Exception as e:
            logger.error(f"❌ Failed to generate response: {e}")
            raise RuntimeError(f"Response generation failed: {e}")

    async def stop(self):
        """Stop the Ollama Hub"""
        try:
            if hasattr(self, 'ollama_hub') and self.ollama_hub:
                await self.ollama_hub.stop()
            self.initialized = False
            logger.info("✅ Ollama Hub stopped")

        except Exception as e:
            logger.error(f"❌ Failed to stop Ollama Hub: {e}")
            
    async def check_model_health(self, model: str) -> Dict[str, Any]:
        """Check model health using real Ollama"""
        try:
            if not self.initialized:
                raise RuntimeError("Ollama Hub not initialized")

            if model not in self.available_models:
                return {'status': 'unavailable', 'health': 0.0, 'model': model}

            # Get real health status from Ollama hub
            health_status = await self.ollama_hub.health_check()

            # Get model-specific performance metrics
            model_performance = await self.ollama_hub.get_model_performance(model)

            return {
                'model': model,
                'status': health_status.get('status', 'unknown'),
                'health': 1.0 if health_status.get('status') == 'healthy' else 0.5,
                'response_time_ms': model_performance.get('avg_response_time', 0),
                'last_check': health_status.get('timestamp', 0)
            }

        except Exception as e:
            logger.error(f"❌ Model health check failed for {model}: {e}")
            return {'status': 'error', 'error': str(e), 'model': model}

    async def get_model_stats(self) -> Dict[str, Any]:
        """Get model statistics from real Ollama"""
        try:
            if not self.initialized:
                raise RuntimeError("Ollama Hub not initialized")

            # Get real system stats from Ollama hub
            system_stats = await self.ollama_hub.get_system_stats()

            stats = {
                'total_models': len(self.available_models),
                'healthy_models': system_stats.get('models', {}).get('active', 0),
                'deployed_models': system_stats.get('models', {}).get('deployed', 0),
                'performance': system_stats.get('performance', {}),
                'resource_usage': system_stats.get('resource_usage', {}),
                'last_updated': system_stats.get('timestamp', 0)
            }

            return stats

        except Exception as e:
            logger.error(f"❌ Failed to get model stats: {e}")
            raise RuntimeError(f"Failed to get model stats: {e}")
