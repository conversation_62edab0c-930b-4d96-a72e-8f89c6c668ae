#!/usr/bin/env python3
"""
PostgreSQL Connection Test Script
Tests the PostgreSQL connection and reports status
"""

import asyncio
import sys
import traceback
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

try:
    import asyncpg
    HAS_ASYNCPG = True
except ImportError:
    HAS_ASYNCPG = False

try:
    import psycopg2
    HAS_PSYCOPG2 = True
except ImportError:
    HAS_PSYCOPG2 = False

async def test_asyncpg_connection():
    """Test PostgreSQL connection using asyncpg"""
    if not HAS_ASYNCPG:
        print("❌ asyncpg not installed")
        return False
    
    try:
        # Connection parameters from config
        conn = await asyncpg.connect(
            host="127.0.0.1",
            port=5432,
            database="postgres",
            user="postgres",
            password="Zaman2009"
        )
        
        # Test query
        result = await conn.fetchval('SELECT 1')
        await conn.close()
        
        if result == 1:
            print("✅ asyncpg connection successful")
            return True
        else:
            print("❌ asyncpg connection failed - unexpected result")
            return False
            
    except Exception as e:
        print(f"❌ asyncpg connection failed: {e}")
        return False

def test_psycopg2_connection():
    """Test PostgreSQL connection using psycopg2"""
    if not HAS_PSYCOPG2:
        print("❌ psycopg2 not installed")
        return False
    
    try:
        # Connection parameters from config
        conn = psycopg2.connect(
            host="127.0.0.1",
            port=5432,
            database="postgres",
            user="postgres",
            password="Zaman2009"
        )
        
        # Test query
        cursor = conn.cursor()
        cursor.execute('SELECT 1')
        result = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        
        if result == 1:
            print("✅ psycopg2 connection successful")
            return True
        else:
            print("❌ psycopg2 connection failed - unexpected result")
            return False
            
    except Exception as e:
        print(f"❌ psycopg2 connection failed: {e}")
        return False

async def test_database_manager():
    """Test the database manager"""
    try:
        from data.database_manager import DatabaseManager
        import yaml
        
        # Load config
        config_path = Path("config/system_config.yaml")
        if not config_path.exists():
            print("❌ Config file not found")
            return False
            
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Test database manager
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        health_status = await db_manager.get_health_status()
        
        if health_status.get('postgres') == 'healthy':
            print("✅ Database Manager PostgreSQL connection successful")
            await db_manager.cleanup()
            return True
        else:
            print(f"❌ Database Manager PostgreSQL status: {health_status.get('postgres')}")
            await db_manager.cleanup()
            return False
            
    except Exception as e:
        print(f"❌ Database Manager test failed: {e}")
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🔍 Testing PostgreSQL Connection...")
    print("=" * 50)
    
    # Check package availability
    print(f"📦 asyncpg available: {HAS_ASYNCPG}")
    print(f"📦 psycopg2 available: {HAS_PSYCOPG2}")
    print()
    
    # Test connections
    results = []
    
    if HAS_ASYNCPG:
        print("🧪 Testing asyncpg connection...")
        result = await test_asyncpg_connection()
        results.append(("asyncpg", result))
        print()
    
    if HAS_PSYCOPG2:
        print("🧪 Testing psycopg2 connection...")
        result = test_psycopg2_connection()
        results.append(("psycopg2", result))
        print()
    
    print("🧪 Testing Database Manager...")
    result = await test_database_manager()
    results.append(("Database Manager", result))
    print()
    
    # Summary
    print("📊 SUMMARY:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 All PostgreSQL tests PASSED!")
        return True
    else:
        print("⚠️ Some PostgreSQL tests FAILED!")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        traceback.print_exc()
        sys.exit(1)
