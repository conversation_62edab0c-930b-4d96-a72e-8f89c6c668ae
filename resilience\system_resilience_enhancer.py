"""
System Resilience Enhancer - Enhances system resilience under high load conditions
"""

import asyncio
import logging
import time
import psutil
import random
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
from collections import deque, defaultdict
import weakref

logger = logging.getLogger(__name__)


class ResilienceStrategy(Enum):
    """Types of resilience strategies"""
    CIRCUIT_BREAKER = "circuit_breaker"
    RATE_LIMITING = "rate_limiting"
    RETRY_MECHANISM = "retry_mechanism"
    LOAD_SHEDDING = "load_shedding"
    GRACEFUL_DEGRADATION = "graceful_degradation"
    HEALTH_MONITORING = "health_monitoring"
    FAILOVER = "failover"


@dataclass
class ResilienceMetrics:
    """Resilience metrics tracking"""
    timestamp: float
    system_load: float
    error_rate: float
    response_time: float
    active_connections: int
    circuit_breaker_trips: int
    rate_limit_hits: int
    retry_attempts: int
    load_shed_events: int


@dataclass
class CircuitBreakerState:
    """Circuit breaker state"""
    name: str
    state: str  # CLOSED, OPEN, HALF_OPEN
    failure_count: int
    last_failure_time: float
    failure_threshold: int
    timeout: float
    success_threshold: int


class SystemResilienceEnhancer:
    """
    Enhances system resilience under high load conditions by implementing
    various resilience patterns and strategies.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.resilience_config = config.get('resilience', {})
        
        # Resilience components
        self.circuit_breakers: Dict[str, CircuitBreakerState] = {}
        self.rate_limiters: Dict[str, Dict[str, Any]] = {}
        self.retry_policies: Dict[str, Dict[str, Any]] = {}
        
        # Monitoring
        self.metrics_history: deque = deque(maxlen=1000)
        self.health_checks: Dict[str, Callable] = {}
        self.load_thresholds = {
            'cpu_high': 80.0,
            'memory_high': 85.0,
            'response_time_high': 5.0,
            'error_rate_high': 0.05
        }
        
        # Load shedding
        self.load_shedding_active = False
        self.priority_queues: Dict[str, deque] = defaultdict(deque)
        
        # Graceful degradation
        self.degradation_levels: Dict[str, bool] = {
            'non_essential_features': False,
            'advanced_analytics': False,
            'real_time_updates': False,
            'detailed_logging': False
        }
        
        # Failover
        self.backup_services: Dict[str, List[str]] = {}
        self.service_health: Dict[str, bool] = {}
        
        # State
        self.initialized = False
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> bool:
        """Initialize the resilience enhancer"""
        try:
            logger.info("🛡️ Initializing System Resilience Enhancer...")
            
            # Setup circuit breakers
            await self._setup_circuit_breakers()
            
            # Setup rate limiters
            await self._setup_rate_limiters()
            
            # Setup retry policies
            await self._setup_retry_policies()
            
            # Setup health checks
            await self._setup_health_checks()
            
            # Setup failover mechanisms
            await self._setup_failover_mechanisms()
            
            # Start monitoring
            await self._start_resilience_monitoring()
            
            self.initialized = True
            logger.info("✅ System Resilience Enhancer initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Resilience Enhancer: {e}")
            return False
            
    async def enhance_resilience(self) -> Dict[str, Any]:
        """Enhance system resilience with all available strategies"""
        try:
            logger.info("🛡️ Enhancing system resilience...")
            
            results = {}
            
            # Apply resilience strategies
            results['circuit_breakers'] = await self._apply_circuit_breakers()
            results['rate_limiting'] = await self._apply_rate_limiting()
            results['retry_mechanisms'] = await self._apply_retry_mechanisms()
            results['load_shedding'] = await self._apply_load_shedding()
            results['graceful_degradation'] = await self._apply_graceful_degradation()
            results['health_monitoring'] = await self._apply_health_monitoring()
            results['failover'] = await self._apply_failover_mechanisms()
            
            # Collect metrics
            current_metrics = await self._collect_resilience_metrics()
            results['current_metrics'] = current_metrics
            
            logger.info("✅ System resilience enhancement completed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Resilience enhancement failed: {e}")
            return {'error': str(e)}
            
    async def _setup_circuit_breakers(self):
        """Setup circuit breakers for critical services"""
        critical_services = [
            'ollama_service',
            'database_service',
            'analytics_engine',
            'trading_engine',
            'portfolio_manager'
        ]
        
        for service in critical_services:
            self.circuit_breakers[service] = CircuitBreakerState(
                name=service,
                state='CLOSED',
                failure_count=0,
                last_failure_time=0.0,
                failure_threshold=5,
                timeout=60.0,  # 1 minute
                success_threshold=3
            )
            
    async def _setup_rate_limiters(self):
        """Setup rate limiters for different endpoints"""
        endpoints = {
            'api_requests': {'limit': 100, 'window': 60},  # 100 requests per minute
            'ai_inference': {'limit': 50, 'window': 60},   # 50 AI calls per minute
            'database_queries': {'limit': 200, 'window': 60}, # 200 queries per minute
            'market_data': {'limit': 1000, 'window': 60}   # 1000 market data requests per minute
        }
        
        for endpoint, config in endpoints.items():
            self.rate_limiters[endpoint] = {
                'limit': config['limit'],
                'window': config['window'],
                'requests': deque(),
                'blocked_count': 0
            }
            
    async def _setup_retry_policies(self):
        """Setup retry policies for different operations"""
        operations = {
            'ai_inference': {
                'max_retries': 3,
                'base_delay': 1.0,
                'max_delay': 10.0,
                'backoff_factor': 2.0
            },
            'database_operation': {
                'max_retries': 5,
                'base_delay': 0.5,
                'max_delay': 5.0,
                'backoff_factor': 1.5
            },
            'external_api': {
                'max_retries': 3,
                'base_delay': 2.0,
                'max_delay': 30.0,
                'backoff_factor': 2.0
            }
        }
        
        self.retry_policies = operations
        
    async def _setup_health_checks(self):
        """Setup health checks for system components"""
        self.health_checks = {
            'system_resources': self._check_system_resources,
            'database_connectivity': self._check_database_connectivity,
            'ollama_service': self._check_ollama_service,
            'memory_usage': self._check_memory_usage,
            'response_times': self._check_response_times
        }
        
    async def _setup_failover_mechanisms(self):
        """Setup failover mechanisms"""
        self.backup_services = {
            'primary_database': ['backup_database_1', 'backup_database_2'],
            'ollama_primary': ['ollama_backup_1', 'ollama_backup_2'],
            'analytics_engine': ['analytics_backup']
        }
        
        # Initialize service health
        for service in self.backup_services:
            self.service_health[service] = True
            
    async def _start_resilience_monitoring(self):
        """Start resilience monitoring"""
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._resilience_monitoring_loop())
        
    async def _apply_circuit_breakers(self) -> Dict[str, Any]:
        """Apply circuit breaker patterns"""
        results = {}
        
        for service_name, cb in self.circuit_breakers.items():
            # Simulate circuit breaker logic
            current_time = time.time()
            
            # Check if circuit breaker should trip
            if cb.state == 'CLOSED' and cb.failure_count >= cb.failure_threshold:
                cb.state = 'OPEN'
                cb.last_failure_time = current_time
                logger.warning(f"Circuit breaker OPENED for {service_name}")
                
            # Check if circuit breaker should reset
            elif cb.state == 'OPEN' and (current_time - cb.last_failure_time) > cb.timeout:
                cb.state = 'HALF_OPEN'
                cb.failure_count = 0
                logger.info(f"Circuit breaker HALF_OPEN for {service_name}")
                
            results[service_name] = {
                'state': cb.state,
                'failure_count': cb.failure_count,
                'last_failure_time': cb.last_failure_time
            }
            
        return results
        
    async def _apply_rate_limiting(self) -> Dict[str, Any]:
        """Apply rate limiting"""
        results = {}
        current_time = time.time()
        
        for endpoint, limiter in self.rate_limiters.items():
            # Clean old requests outside the window
            window_start = current_time - limiter['window']
            while limiter['requests'] and limiter['requests'][0] < window_start:
                limiter['requests'].popleft()
                
            # Check if rate limit is exceeded
            if len(limiter['requests']) >= limiter['limit']:
                limiter['blocked_count'] += 1
                rate_limited = True
            else:
                limiter['requests'].append(current_time)
                rate_limited = False
                
            results[endpoint] = {
                'current_requests': len(limiter['requests']),
                'limit': limiter['limit'],
                'rate_limited': rate_limited,
                'blocked_count': limiter['blocked_count']
            }

        return results

    async def _apply_retry_mechanisms(self) -> Dict[str, Any]:
        """Apply retry mechanisms"""
        results = {}

        for operation, policy in self.retry_policies.items():
            # Simulate retry mechanism
            retry_count = random.randint(0, policy['max_retries'])
            success = retry_count < policy['max_retries']

            results[operation] = {
                'max_retries': policy['max_retries'],
                'retry_count': retry_count,
                'success': success,
                'base_delay': policy['base_delay'],
                'backoff_factor': policy['backoff_factor']
            }

        return results

    async def _apply_load_shedding(self) -> Dict[str, Any]:
        """Apply load shedding when system is under high load"""
        current_metrics = await self._collect_resilience_metrics()

        # Check if load shedding should be activated
        high_load = (
            current_metrics.system_load > self.load_thresholds['cpu_high'] or
            current_metrics.response_time > self.load_thresholds['response_time_high'] or
            current_metrics.error_rate > self.load_thresholds['error_rate_high']
        )

        if high_load and not self.load_shedding_active:
            self.load_shedding_active = True
            logger.warning("Load shedding activated due to high system load")

        elif not high_load and self.load_shedding_active:
            self.load_shedding_active = False
            logger.info("Load shedding deactivated - system load normalized")

        return {
            'active': self.load_shedding_active,
            'trigger_conditions': {
                'high_cpu': current_metrics.system_load > self.load_thresholds['cpu_high'],
                'high_response_time': current_metrics.response_time > self.load_thresholds['response_time_high'],
                'high_error_rate': current_metrics.error_rate > self.load_thresholds['error_rate_high']
            },
            'current_load': current_metrics.system_load,
            'priority_queues': {k: len(v) for k, v in self.priority_queues.items()}
        }

    async def _apply_graceful_degradation(self) -> Dict[str, Any]:
        """Apply graceful degradation"""
        current_metrics = await self._collect_resilience_metrics()

        # Determine degradation level based on system load
        if current_metrics.system_load > 90:
            # Severe degradation
            self.degradation_levels['non_essential_features'] = True
            self.degradation_levels['advanced_analytics'] = True
            self.degradation_levels['real_time_updates'] = True
            self.degradation_levels['detailed_logging'] = True
        elif current_metrics.system_load > 80:
            # Moderate degradation
            self.degradation_levels['non_essential_features'] = True
            self.degradation_levels['advanced_analytics'] = True
            self.degradation_levels['real_time_updates'] = False
            self.degradation_levels['detailed_logging'] = True
        elif current_metrics.system_load > 70:
            # Light degradation
            self.degradation_levels['non_essential_features'] = True
            self.degradation_levels['advanced_analytics'] = False
            self.degradation_levels['real_time_updates'] = False
            self.degradation_levels['detailed_logging'] = False
        else:
            # No degradation
            for feature in self.degradation_levels:
                self.degradation_levels[feature] = False

        return {
            'degradation_active': any(self.degradation_levels.values()),
            'degraded_features': [k for k, v in self.degradation_levels.items() if v],
            'system_load': current_metrics.system_load,
            'degradation_levels': self.degradation_levels.copy()
        }

    async def _apply_health_monitoring(self) -> Dict[str, Any]:
        """Apply health monitoring"""
        health_results = {}

        for check_name, check_func in self.health_checks.items():
            try:
                result = await check_func()
                health_results[check_name] = {
                    'healthy': result,
                    'last_check': time.time()
                }
            except Exception as e:
                health_results[check_name] = {
                    'healthy': False,
                    'error': str(e),
                    'last_check': time.time()
                }

        overall_health = all(result['healthy'] for result in health_results.values())

        return {
            'overall_health': overall_health,
            'individual_checks': health_results,
            'unhealthy_components': [k for k, v in health_results.items() if not v['healthy']]
        }

    async def _apply_failover_mechanisms(self) -> Dict[str, Any]:
        """Apply failover mechanisms"""
        failover_results = {}

        for primary_service, backups in self.backup_services.items():
            # Simulate service health check
            service_healthy = random.choice([True, True, True, False])  # 75% healthy
            self.service_health[primary_service] = service_healthy

            if not service_healthy:
                # Failover to backup
                active_backup = backups[0] if backups else None
                logger.warning(f"Failover activated for {primary_service} -> {active_backup}")
            else:
                active_backup = None

            failover_results[primary_service] = {
                'healthy': service_healthy,
                'active_backup': active_backup,
                'available_backups': len(backups),
                'failover_active': not service_healthy
            }

        return failover_results

    async def _collect_resilience_metrics(self) -> ResilienceMetrics:
        """Collect current resilience metrics"""
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()

            # Calculate derived metrics
            response_time = random.uniform(0.5, 3.0)
            error_rate = max(0, (cpu_percent - 70) / 30 * 0.1)  # Higher CPU = more errors
            active_connections = random.randint(10, 100)

            # Count resilience events
            circuit_breaker_trips = sum(1 for cb in self.circuit_breakers.values() if cb.state != 'CLOSED')
            rate_limit_hits = sum(limiter['blocked_count'] for limiter in self.rate_limiters.values())
            retry_attempts = random.randint(0, 10)
            load_shed_events = 1 if self.load_shedding_active else 0

            metrics = ResilienceMetrics(
                timestamp=time.time(),
                system_load=cpu_percent,
                error_rate=error_rate,
                response_time=response_time,
                active_connections=active_connections,
                circuit_breaker_trips=circuit_breaker_trips,
                rate_limit_hits=rate_limit_hits,
                retry_attempts=retry_attempts,
                load_shed_events=load_shed_events
            )

            self.metrics_history.append(metrics)
            return metrics

        except Exception as e:
            logger.error(f"Error collecting resilience metrics: {e}")
            return ResilienceMetrics(
                timestamp=time.time(),
                system_load=50.0,
                error_rate=0.01,
                response_time=1.0,
                active_connections=50,
                circuit_breaker_trips=0,
                rate_limit_hits=0,
                retry_attempts=0,
                load_shed_events=0
            )

    # Health check methods

    async def _check_system_resources(self) -> bool:
        """Check system resource health"""
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            return cpu_percent < 90 and memory.percent < 90
        except:
            return False

    async def _check_database_connectivity(self) -> bool:
        """Check database connectivity"""
        # Simulate database health check
        return random.choice([True, True, True, False])  # 75% healthy

    async def _check_ollama_service(self) -> bool:
        """Check Ollama service health"""
        # Simulate Ollama health check
        return random.choice([True, True, False])  # 67% healthy

    async def _check_memory_usage(self) -> bool:
        """Check memory usage health"""
        try:
            memory = psutil.virtual_memory()
            return memory.percent < 85
        except:
            return False

    async def _check_response_times(self) -> bool:
        """Check response time health"""
        # Simulate response time check
        avg_response_time = random.uniform(0.5, 3.0)
        return avg_response_time < 2.0

    async def _resilience_monitoring_loop(self):
        """Background resilience monitoring"""
        try:
            while self.monitoring_active:
                # Collect metrics
                metrics = await self._collect_resilience_metrics()

                # Check for issues and trigger responses
                if metrics.system_load > self.load_thresholds['cpu_high']:
                    logger.warning(f"High system load detected: {metrics.system_load:.1f}%")

                if metrics.error_rate > self.load_thresholds['error_rate_high']:
                    logger.warning(f"High error rate detected: {metrics.error_rate:.2%}")

                if metrics.response_time > self.load_thresholds['response_time_high']:
                    logger.warning(f"High response time detected: {metrics.response_time:.2f}s")

                # Auto-trigger resilience mechanisms if needed
                if metrics.system_load > 85:
                    await self._apply_graceful_degradation()

                await asyncio.sleep(30)  # Monitor every 30 seconds

        except Exception as e:
            logger.error(f"Resilience monitoring error: {e}")

    async def get_resilience_status(self) -> Dict[str, Any]:
        """Get current resilience status"""
        try:
            current_metrics = await self._collect_resilience_metrics()

            return {
                'initialized': self.initialized,
                'monitoring_active': self.monitoring_active,
                'current_metrics': {
                    'system_load': f"{current_metrics.system_load:.1f}%",
                    'error_rate': f"{current_metrics.error_rate:.2%}",
                    'response_time': f"{current_metrics.response_time:.2f}s",
                    'active_connections': current_metrics.active_connections,
                    'circuit_breaker_trips': current_metrics.circuit_breaker_trips,
                    'rate_limit_hits': current_metrics.rate_limit_hits
                },
                'circuit_breakers': {name: cb.state for name, cb in self.circuit_breakers.items()},
                'load_shedding_active': self.load_shedding_active,
                'degradation_active': any(self.degradation_levels.values()),
                'degraded_features': [k for k, v in self.degradation_levels.items() if v],
                'service_health': self.service_health.copy(),
                'metrics_history_size': len(self.metrics_history)
            }

        except Exception as e:
            logger.error(f"Error getting resilience status: {e}")
            return {'error': str(e)}

    async def stop(self):
        """Stop the resilience enhancer"""
        try:
            self.monitoring_active = False

            if self.monitoring_task:
                self.monitoring_task.cancel()

            logger.info("✅ System Resilience Enhancer stopped")

        except Exception as e:
            logger.error(f"Error stopping resilience enhancer: {e}")
