"""
Trading Engine - Core trading execution and order management system
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)


class OrderType(Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """Order status"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class Order:
    """Trading order"""
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    average_fill_price: float = 0.0
    timestamp: float = 0.0
    agent_id: Optional[str] = None
    strategy_id: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()
        if self.metadata is None:
            self.metadata = {}


@dataclass
class Trade:
    """Executed trade"""
    id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: float
    commission: float = 0.0
    agent_id: Optional[str] = None
    strategy_id: Optional[str] = None


@dataclass
class Position:
    """Trading position"""
    symbol: str
    quantity: float
    average_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    last_updated: float


class TradingEngine:
    """
    Core trading engine that handles order execution, position management,
    and trade tracking with real market simulation capabilities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.trading_config = config.get('trading', {})
        
        # Order management
        self.orders: Dict[str, Order] = {}
        self.trades: Dict[str, Trade] = {}
        self.positions: Dict[str, Position] = {}
        
        # Market data simulation
        self.market_prices: Dict[str, float] = {
            'AAPL': 150.0,
            'GOOGL': 2800.0,
            'MSFT': 350.0,
            'TSLA': 200.0,
            'AMZN': 3200.0,
            'NVDA': 800.0,
            'META': 300.0,
            'NFLX': 400.0,
            'SPY': 450.0,
            'QQQ': 380.0
        }
        
        # Trading parameters
        self.commission_rate = self.trading_config.get('commission_rate', 0.001)  # 0.1%
        self.slippage_rate = self.trading_config.get('slippage_rate', 0.0005)    # 0.05%
        self.max_position_size = self.trading_config.get('max_position_size', 1000000)  # $1M
        self.available_cash = self.trading_config.get('initial_cash', 1000000)  # $1M
        
        # Risk controls
        self.daily_loss_limit = self.trading_config.get('daily_loss_limit', 50000)  # $50K
        self.position_limit_per_symbol = self.trading_config.get('position_limit_per_symbol', 100000)  # $100K
        
        # Performance tracking
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.total_trades = 0
        self.successful_trades = 0
        
        # State
        self.initialized = False
        self.trading_active = True
        
    async def initialize(self) -> bool:
        """Initialize the trading engine"""
        try:
            logger.info("🚀 Initializing Trading Engine...")
            
            # Initialize market data
            await self._initialize_market_data()
            
            # Setup risk controls
            await self._setup_risk_controls()
            
            # Initialize position tracking
            await self._initialize_position_tracking()
            
            # Start market simulation
            await self._start_market_simulation()
            
            self.initialized = True
            logger.info("✅ Trading Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Trading Engine: {e}")
            return False
            
    async def submit_order(self, symbol: str, side: OrderSide, order_type: OrderType, 
                          quantity: float, price: Optional[float] = None, 
                          stop_price: Optional[float] = None, agent_id: Optional[str] = None,
                          strategy_id: Optional[str] = None) -> Optional[str]:
        """Submit a trading order"""
        try:
            # Generate order ID
            order_id = str(uuid.uuid4())
            
            # Create order
            order = Order(
                id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                stop_price=stop_price,
                agent_id=agent_id,
                strategy_id=strategy_id
            )
            
            # Validate order
            validation_result = await self._validate_order(order)
            if not validation_result['valid']:
                logger.warning(f"Order validation failed: {validation_result['reason']}")
                order.status = OrderStatus.REJECTED
                self.orders[order_id] = order
                return order_id
                
            # Submit order
            order.status = OrderStatus.SUBMITTED
            self.orders[order_id] = order
            
            logger.info(f"✅ Order submitted: {order_id} - {side.value} {quantity} {symbol}")
            
            # Process order asynchronously
            asyncio.create_task(self._process_order(order_id))
            
            return order_id
            
        except Exception as e:
            logger.error(f"❌ Failed to submit order: {e}")
            return None
            
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            if order_id not in self.orders:
                logger.warning(f"Order not found: {order_id}")
                return False
                
            order = self.orders[order_id]
            
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                logger.warning(f"Cannot cancel order in status: {order.status}")
                return False
                
            order.status = OrderStatus.CANCELLED
            logger.info(f"✅ Order cancelled: {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to cancel order: {e}")
            return False
            
    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order status"""
        try:
            if order_id not in self.orders:
                return None
                
            order = self.orders[order_id]
            return {
                'id': order.id,
                'symbol': order.symbol,
                'side': order.side.value,
                'order_type': order.order_type.value,
                'quantity': order.quantity,
                'price': order.price,
                'status': order.status.value,
                'filled_quantity': order.filled_quantity,
                'average_fill_price': order.average_fill_price,
                'timestamp': order.timestamp
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get order status: {e}")
            return None
            
    async def get_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get current positions"""
        try:
            positions_dict = {}
            
            for symbol, position in self.positions.items():
                positions_dict[symbol] = {
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'average_price': position.average_price,
                    'market_value': position.market_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'realized_pnl': position.realized_pnl,
                    'last_updated': position.last_updated
                }
                
            return positions_dict
            
        except Exception as e:
            logger.error(f"❌ Failed to get positions: {e}")
            return {}
            
    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary"""
        try:
            total_market_value = sum(pos.market_value for pos in self.positions.values())
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            total_realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())
            
            return {
                'available_cash': self.available_cash,
                'total_market_value': total_market_value,
                'total_portfolio_value': self.available_cash + total_market_value,
                'total_unrealized_pnl': total_unrealized_pnl,
                'total_realized_pnl': total_realized_pnl,
                'daily_pnl': self.daily_pnl,
                'total_pnl': self.total_pnl,
                'total_trades': self.total_trades,
                'successful_trades': self.successful_trades,
                'win_rate': self.successful_trades / max(1, self.total_trades),
                'positions_count': len(self.positions),
                'trading_active': self.trading_active
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get portfolio summary: {e}")
            return {}
            
    async def _initialize_market_data(self):
        """Initialize market data simulation"""
        # Add some volatility to prices
        import random
        for symbol in self.market_prices:
            volatility = random.uniform(0.95, 1.05)
            self.market_prices[symbol] *= volatility
            
    async def _setup_risk_controls(self):
        """Setup risk control parameters"""
        # Risk controls are already configured in __init__
        pass
        
    async def _initialize_position_tracking(self):
        """Initialize position tracking"""
        # Positions will be created as trades are executed
        pass
        
    async def _start_market_simulation(self):
        """Start market price simulation"""
        asyncio.create_task(self._market_simulation_loop())
        
    async def _market_simulation_loop(self):
        """Market price simulation loop"""
        try:
            while self.trading_active:
                await asyncio.sleep(1)  # Update every second
                
                # Simulate price movements
                import random
                for symbol in self.market_prices:
                    change = random.uniform(-0.002, 0.002)  # ±0.2% per second
                    self.market_prices[symbol] *= (1 + change)
                    self.market_prices[symbol] = max(0.01, self.market_prices[symbol])  # Prevent negative prices
                    
                # Update position values
                await self._update_position_values()
                
        except Exception as e:
            logger.error(f"Market simulation error: {e}")
            
    async def _validate_order(self, order: Order) -> Dict[str, Any]:
        """Validate order before submission"""
        try:
            # Check if symbol exists
            if order.symbol not in self.market_prices:
                return {'valid': False, 'reason': f'Unknown symbol: {order.symbol}'}
                
            # Check quantity
            if order.quantity <= 0:
                return {'valid': False, 'reason': 'Quantity must be positive'}
                
            # Check cash availability for buy orders
            if order.side == OrderSide.BUY:
                estimated_cost = order.quantity * (order.price or self.market_prices[order.symbol])
                if estimated_cost > self.available_cash:
                    return {'valid': False, 'reason': 'Insufficient cash'}
                    
            # Check position limits
            estimated_value = order.quantity * (order.price or self.market_prices[order.symbol])
            if estimated_value > self.position_limit_per_symbol:
                return {'valid': False, 'reason': 'Position limit exceeded'}
                
            # Check daily loss limit
            if self.daily_pnl < -self.daily_loss_limit:
                return {'valid': False, 'reason': 'Daily loss limit reached'}
                
            return {'valid': True, 'reason': 'Order validated'}
            
        except Exception as e:
            logger.error(f"Order validation error: {e}")
            return {'valid': False, 'reason': f'Validation error: {e}'}
            
    async def _process_order(self, order_id: str):
        """Process order execution"""
        try:
            order = self.orders[order_id]
            
            # Simulate order processing delay
            await asyncio.sleep(0.1)
            
            # Get current market price
            current_price = self.market_prices[order.symbol]
            
            # Determine execution price
            if order.order_type == OrderType.MARKET:
                execution_price = current_price
                # Apply slippage
                if order.side == OrderSide.BUY:
                    execution_price *= (1 + self.slippage_rate)
                else:
                    execution_price *= (1 - self.slippage_rate)
            elif order.order_type == OrderType.LIMIT:
                if order.side == OrderSide.BUY and current_price <= order.price:
                    execution_price = order.price
                elif order.side == OrderSide.SELL and current_price >= order.price:
                    execution_price = order.price
                else:
                    # Order not filled yet
                    return
            else:
                # For now, treat other order types as market orders
                execution_price = current_price
                
            # Execute the trade
            await self._execute_trade(order, execution_price)
            
        except Exception as e:
            logger.error(f"Order processing error: {e}")
            order.status = OrderStatus.REJECTED
            
    async def _execute_trade(self, order: Order, execution_price: float):
        """Execute a trade"""
        try:
            # Calculate commission
            trade_value = order.quantity * execution_price
            commission = trade_value * self.commission_rate
            
            # Create trade record
            trade_id = str(uuid.uuid4())
            trade = Trade(
                id=trade_id,
                order_id=order.id,
                symbol=order.symbol,
                side=order.side,
                quantity=order.quantity,
                price=execution_price,
                timestamp=time.time(),
                commission=commission,
                agent_id=order.agent_id,
                strategy_id=order.strategy_id
            )
            
            self.trades[trade_id] = trade
            
            # Update order
            order.status = OrderStatus.FILLED
            order.filled_quantity = order.quantity
            order.average_fill_price = execution_price
            
            # Update positions
            await self._update_position(trade)
            
            # Update cash
            if order.side == OrderSide.BUY:
                self.available_cash -= (trade_value + commission)
            else:
                self.available_cash += (trade_value - commission)
                
            # Update performance metrics
            self.total_trades += 1
            if order.side == OrderSide.SELL:
                # Calculate realized P&L for sell orders
                if order.symbol in self.positions:
                    position = self.positions[order.symbol]
                    realized_pnl = (execution_price - position.average_price) * order.quantity - commission
                    position.realized_pnl += realized_pnl
                    self.total_pnl += realized_pnl
                    self.daily_pnl += realized_pnl
                    
                    if realized_pnl > 0:
                        self.successful_trades += 1
                        
            logger.info(f"✅ Trade executed: {trade_id} - {order.side.value} {order.quantity} {order.symbol} @ ${execution_price:.2f}")
            
        except Exception as e:
            logger.error(f"Trade execution error: {e}")
            order.status = OrderStatus.REJECTED
            
    async def _update_position(self, trade: Trade):
        """Update position based on trade"""
        try:
            symbol = trade.symbol
            
            if symbol not in self.positions:
                # Create new position
                self.positions[symbol] = Position(
                    symbol=symbol,
                    quantity=0.0,
                    average_price=0.0,
                    market_value=0.0,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    last_updated=time.time()
                )
                
            position = self.positions[symbol]
            
            if trade.side == OrderSide.BUY:
                # Add to position
                total_cost = position.quantity * position.average_price + trade.quantity * trade.price
                position.quantity += trade.quantity
                position.average_price = total_cost / position.quantity if position.quantity > 0 else 0
            else:
                # Reduce position
                position.quantity -= trade.quantity
                if position.quantity <= 0:
                    # Position closed
                    if position.quantity < 0:
                        # Short position (for simplicity, we'll just set to 0)
                        position.quantity = 0
                        position.average_price = 0
                        
            position.last_updated = time.time()
            await self._update_position_values()
            
        except Exception as e:
            logger.error(f"Position update error: {e}")
            
    async def _update_position_values(self):
        """Update position market values and unrealized P&L"""
        try:
            for symbol, position in self.positions.items():
                if position.quantity > 0:
                    current_price = self.market_prices.get(symbol, position.average_price)
                    position.market_value = position.quantity * current_price
                    position.unrealized_pnl = (current_price - position.average_price) * position.quantity
                else:
                    position.market_value = 0.0
                    position.unrealized_pnl = 0.0
                    
        except Exception as e:
            logger.error(f"Position value update error: {e}")
            
    async def get_trading_status(self) -> Dict[str, Any]:
        """Get current trading engine status"""
        try:
            return {
                'initialized': self.initialized,
                'trading_active': self.trading_active,
                'total_orders': len(self.orders),
                'total_trades': len(self.trades),
                'active_positions': len([p for p in self.positions.values() if p.quantity > 0]),
                'available_cash': self.available_cash,
                'daily_pnl': self.daily_pnl,
                'total_pnl': self.total_pnl,
                'market_symbols': list(self.market_prices.keys()),
                'commission_rate': self.commission_rate,
                'slippage_rate': self.slippage_rate
            }
            
        except Exception as e:
            logger.error(f"Error getting trading status: {e}")
            return {'error': str(e)}
            
    async def stop(self):
        """Stop the trading engine"""
        try:
            self.trading_active = False
            logger.info("✅ Trading Engine stopped")
            
        except Exception as e:
            logger.error(f"Error stopping trading engine: {e}")
